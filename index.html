<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WarpSpace - Reality Warping Shooter</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
            color: white;
        }
        
        #appContainer {
            position: relative;
            width: 800px;
            height: 600px;
        }
        
        #mainMenu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        
        .main-menu {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
        }
        
        .menu-header h1 {
            font-size: 3rem;
            margin: 0 0 0.5rem 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .menu-header p {
            font-size: 1.2rem;
            color: #ccc;
            margin: 0 0 2rem 0;
        }
        
        .auth-section, .user-section, .loading-section, .error-section {
            margin: 2rem 0;
        }
        
        .user-info {
            margin-bottom: 2rem;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 1rem;
        }
        
        .user-info h2 {
            margin: 0.5rem 0;
            color: #4ecdc4;
        }
        
        .user-email {
            color: #999;
            font-size: 0.9rem;
        }
        
        .menu-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }
        
        .primary-button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            min-width: 200px;
        }
        
        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .secondary-button {
            background: transparent;
            border: 2px solid #666;
            color: #ccc;
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 150px;
        }
        
        .secondary-button:hover {
            border-color: #4ecdc4;
            color: #4ecdc4;
        }
        
        .debug-section {
            margin-top: 2rem;
            padding: 1rem;
            border: 1px solid #444;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .debug-section h3 {
            margin: 0 0 0.5rem 0;
            color: #ff9800;
        }
        
        .debug-info {
            margin: 1rem 0;
            font-size: 0.9rem;
            color: #ccc;
        }
        
        .debug-info p {
            margin: 0.25rem 0;
        }
        
        .debug-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .debug-button {
            background: #ff9800;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .debug-button:hover {
            background: #f57c00;
        }
        
        .debug-note {
            font-size: 0.8rem;
            color: #999;
            margin: 0.5rem 0 0 0;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #333;
            border-top: 4px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-section {
            color: #ff6b6b;
        }
        
        .error-message {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid #ff6b6b;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .menu-footer {
            margin-top: 2rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        #gameContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 2px solid #333;
            display: none;
        }
        
        #gameCanvas {
            display: block;
            background: #000;
            width: 100%;
            height: 100%;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 14px;
            z-index: 10;
        }
        
        /* Orange ID widget styling */
        #bedrock-login-widget {
            margin: 1rem 0;
        }
        
        .orange-id-fallback {
            text-align: center;
            padding: 2rem;
            border: 1px solid #444;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            margin: 1rem 0;
        }
        
        .fallback-message h3 {
            color: #ff6b6b;
            margin: 0 0 1rem 0;
        }
        
        .fallback-message p {
            color: #ccc;
            margin: 0.5rem 0;
        }
        
        .fallback-debug {
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(255, 152, 0, 0.1);
            border-radius: 5px;
        }
        
        .fallback-actions {
            margin: 1rem 0;
        }
        
        .fallback-note {
            font-size: 0.9rem;
            color: #999;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div id="appContainer">
        <!-- Main Menu (shown initially) -->
        <div id="mainMenu">
            <div class="main-menu">
                <div class="menu-header">
                    <h1>WarpSpace</h1>
                    <p>Loading...</p>
                </div>
            </div>
        </div>
        
        <!-- Game Container (hidden initially) -->
        <div id="gameContainer">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            <div id="ui">
                <div>WarpSpace - Loading...</div>
            </div>
        </div>
    </div>
    
    <!-- Orange ID Dependencies -->
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://public-cdn-files.pages.dev/bedrock-passport.umd.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Game Application -->
    <script type="module" src="/src/main.js"></script>
</body>
</html>