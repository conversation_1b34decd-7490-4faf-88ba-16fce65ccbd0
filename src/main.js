import { GameEngine } from './engine/GameEngine.js';
import { AuthManager } from './auth/AuthManager.js';
import { MainMenu } from './ui/MainMenu.js';

/**
 * WarpSpace Application
 * Main application controller that manages authentication and game flow
 */
class WarpSpaceApp {
    constructor() {
        this.authManager = null;
        this.mainMenu = null;
        this.gameEngine = null;
        
        // UI elements
        this.mainMenuContainer = null;
        this.gameContainer = null;
        this.canvas = null;
        this.ui = null;
        
        // Application state
        this.currentUser = null;
        this.isGameRunning = false;
    }
    
    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('Initializing WarpSpace application...');
            
            // Get UI elements
            this.mainMenuContainer = document.getElementById('mainMenu');
            this.gameContainer = document.getElementById('gameContainer');
            this.canvas = document.getElementById('gameCanvas');
            this.ui = document.getElementById('ui');
            
            if (!this.mainMenuContainer || !this.gameContainer || !this.canvas) {
                throw new Error('Required UI elements not found');
            }
            
            // Initialize authentication manager
            this.authManager = new AuthManager({
                // These would be replaced with actual values in production
                tenantId: 'orange-abc123',
                subscriptionKey: 'your_API_Key'
            });
            
            // Initialize main menu
            this.mainMenu = new MainMenu(this.mainMenuContainer, this.authManager);
            
            // Set up main menu callbacks
            this.mainMenu.setOnGameStart((user) => {
                this.startGame(user);
            });
            
            this.mainMenu.setOnAuthComplete((user) => {
                this.handleAuthComplete(user);
            });
            
            // Initialize main menu
            await this.mainMenu.init();
            
            // Show main menu
            this.showMainMenu();
            
            console.log('WarpSpace application initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize WarpSpace application:', error);
            this.showError(error);
        }
    }
    
    /**
     * Show main menu
     */
    showMainMenu() {
        this.mainMenuContainer.style.display = 'flex';
        this.gameContainer.style.display = 'none';
        this.isGameRunning = false;
        
        if (this.gameEngine) {
            this.gameEngine.pause();
        }
    }
    
    /**
     * Show game
     */
    showGame() {
        this.mainMenuContainer.style.display = 'none';
        this.gameContainer.style.display = 'block';
        this.isGameRunning = true;
        
        if (this.gameEngine) {
            this.gameEngine.resume();
        }
    }
    
    /**
     * Handle authentication completion
     */
    handleAuthComplete(user) {
        console.log('Authentication completed for user:', user);
        this.currentUser = user;
    }
    
    /**
     * Start the game
     */
    async startGame(user) {
        try {
            console.log('Starting game for user:', user);
            this.currentUser = user;
            
            // Initialize game engine if not already done
            if (!this.gameEngine) {
                this.gameEngine = new GameEngine(this.canvas, this.ui);
                
                // Set up game engine event handlers
                this.setupGameEngineHandlers();
                
                // Initialize game engine
                await this.gameEngine.init();
            }
            
            // Show game view
            this.showGame();
            
            // Update UI with user info
            this.updateGameUI();
            
            console.log('Game started successfully');
            
        } catch (error) {
            console.error('Failed to start game:', error);
            this.showError(error);
        }
    }
    
    /**
     * Set up game engine event handlers
     */
    setupGameEngineHandlers() {
        // Handle window resize
        window.addEventListener('resize', () => {
            if (this.gameEngine) {
                this.gameEngine.handleResize();
            }
        });
        
        // Handle visibility change for pause/resume
        document.addEventListener('visibilitychange', () => {
            if (!this.isGameRunning || !this.gameEngine) return;
            
            if (document.hidden) {
                this.gameEngine.pause();
            } else {
                this.gameEngine.resume();
            }
        });
        
        // Handle escape key to return to main menu
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.isGameRunning) {
                this.returnToMainMenu();
            }
        });
    }
    
    /**
     * Update game UI with user information
     */
    updateGameUI() {
        if (!this.currentUser || !this.ui) return;
        
        // Add user info to game UI
        const userInfo = document.createElement('div');
        userInfo.style.position = 'absolute';
        userInfo.style.top = '10px';
        userInfo.style.right = '10px';
        userInfo.style.color = 'white';
        userInfo.style.fontSize = '12px';
        userInfo.style.textAlign = 'right';
        userInfo.innerHTML = `
            <div>Player: ${this.currentUser.displayName || this.currentUser.name}</div>
            <div style="font-size: 10px; color: #ccc;">Press ESC for menu</div>
        `;
        
        // Remove existing user info if present
        const existingUserInfo = this.ui.querySelector('.user-info-overlay');
        if (existingUserInfo) {
            existingUserInfo.remove();
        }
        
        userInfo.className = 'user-info-overlay';
        this.ui.appendChild(userInfo);
    }
    
    /**
     * Return to main menu
     */
    returnToMainMenu() {
        console.log('Returning to main menu');
        this.showMainMenu();
    }
    
    /**
     * Show error message
     */
    showError(error) {
        console.error('Application error:', error);
        
        if (this.mainMenuContainer) {
            this.mainMenuContainer.innerHTML = `
                <div class="main-menu">
                    <div class="menu-header">
                        <h1>WarpSpace</h1>
                        <p>Reality Warping Shooter</p>
                    </div>
                    
                    <div class="error-section">
                        <h2>Application Error</h2>
                        <p class="error-message">${error.message || 'An unknown error occurred'}</p>
                        <button onclick="window.location.reload()" class="primary-button">Reload Application</button>
                    </div>
                </div>
            `;
            this.showMainMenu();
        }
    }
    
    /**
     * Cleanup application resources
     */
    destroy() {
        if (this.gameEngine) {
            this.gameEngine.destroy();
            this.gameEngine = null;
        }
        
        if (this.mainMenu) {
            this.mainMenu.destroy();
            this.mainMenu = null;
        }
        
        console.log('WarpSpace application destroyed');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    const app = new WarpSpaceApp();
    
    // Make app globally available for debugging
    window.warpSpaceApp = app;
    
    // Initialize application
    await app.init();
    
    // Handle page unload
    window.addEventListener('beforeunload', () => {
        app.destroy();
    });
});