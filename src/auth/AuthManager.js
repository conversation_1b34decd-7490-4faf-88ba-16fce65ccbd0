/**
 * AuthManager - Orange ID authentication wrapper
 * Handles Orange ID integration with authentication state management
 */
export class AuthManager {
    constructor(config = {}) {
        this.config = {
            baseUrl: 'https://api.bedrockpassport.com',
            authCallbackUrl: window.location.origin,
            tenantId: config.tenantId || 'orange-abc123',
            subscriptionKey: config.subscriptionKey || 'your_API_Key',
            ...config
        };
        
        // Authentication state
        this.isAuthenticated = false;
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        
        // Callbacks
        this.onAuthStateChange = null;
        this.onAuthError = null;
        
        // Debug mode
        this.debugMode = this.detectDebugMode();
        
        console.log('AuthManager initialized', { debugMode: this.debugMode });
    }
    
    /**
     * Detect if we're in debug mode
     */
    detectDebugMode() {
        // Check for localhost, development environment, or debug parameter
        const isLocalhost = window.location.hostname === 'localhost' || 
                           window.location.hostname === '127.0.0.1' ||
                           window.location.hostname === '';
        
        const hasDebugParam = new URLSearchParams(window.location.search).has('debug');
        const isDevelopment = window.location.port !== '' || 
                             window.location.hostname.includes('localhost') ||
                             window.location.hostname.includes('127.0.0.1');
        
        // Check for development build indicators
        const isDevBuild = document.querySelector('script[src*="vite"]') !== null ||
                          window.location.protocol === 'http:' && isLocalhost;
        
        // Check environment variables if available
        const isNodeDev = typeof process !== 'undefined' && process?.env?.NODE_ENV === 'development';
        
        const debugMode = isLocalhost || hasDebugParam || isDevelopment || isDevBuild || isNodeDev;
        
        console.log('Debug mode detection:', {
            isLocalhost,
            hasDebugParam,
            isDevelopment,
            isDevBuild,
            isNodeDev,
            debugMode
        });
        
        return debugMode;
    }
    
    /**
     * Initialize authentication system
     */
    async init() {
        try {
            console.log('AuthManager.init() called');
            
            // Check if we're handling an auth callback
            const params = new URLSearchParams(window.location.search);
            const token = params.get('token');
            const refreshToken = params.get('refreshToken');
            
            if (token && refreshToken) {
                console.log('Processing authentication callback...');
                return await this.handleAuthCallback(token, refreshToken);
            }
            
            // Check for existing authentication in localStorage
            const savedAuth = this.loadSavedAuth();
            if (savedAuth) {
                console.log('Found saved authentication, validating...');
                const isValid = await this.validateSavedAuth(savedAuth);
                if (isValid) {
                    return true;
                }
            }
            
            console.log('No existing authentication found, user needs to authenticate');
            return false;
            
        } catch (error) {
            console.error('Auth initialization error:', error);
            this.handleAuthError(error);
            return false;
        }
    }
    
    /**
     * Handle authentication callback from Orange ID
     */
    async handleAuthCallback(token, refreshToken) {
        try {
            // Validate tokens with Orange ID
            const user = await this.validateTokens(token, refreshToken);
            
            if (user) {
                this.setAuthenticatedState(user, token, refreshToken);
                this.saveAuth(user, token, refreshToken);
                
                // Clean up URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
                
                console.log('Authentication successful:', user);
                return true;
            } else {
                throw new Error('Token validation failed');
            }
            
        } catch (error) {
            console.error('Auth callback error:', error);
            this.handleAuthError(error);
            return false;
        }
    }
    
    /**
     * Validate tokens with Orange ID API
     */
    async validateTokens(token, refreshToken) {
        try {
            const response = await fetch(`${this.config.baseUrl}/api/v1/auth/user`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const user = await response.json();
                return user;
            } else {
                throw new Error(`Token validation failed: ${response.status}`);
            }
            
        } catch (error) {
            console.error('Token validation error:', error);
            throw error;
        }
    }
    
    /**
     * Load saved authentication from localStorage
     */
    loadSavedAuth() {
        try {
            const saved = localStorage.getItem('warpspace_auth');
            return saved ? JSON.parse(saved) : null;
        } catch (error) {
            console.error('Error loading saved auth:', error);
            return null;
        }
    }
    
    /**
     * Validate saved authentication
     */
    async validateSavedAuth(savedAuth) {
        try {
            if (!savedAuth.token || !savedAuth.refreshToken) {
                return false;
            }
            
            // Try to validate the current token
            const user = await this.validateTokens(savedAuth.token, savedAuth.refreshToken);
            
            if (user) {
                this.setAuthenticatedState(user, savedAuth.token, savedAuth.refreshToken);
                return true;
            } else {
                // Token expired, try to refresh
                return await this.refreshAuthToken(savedAuth.refreshToken);
            }
            
        } catch (error) {
            console.error('Saved auth validation error:', error);
            this.clearSavedAuth();
            return false;
        }
    }
    
    /**
     * Refresh authentication token
     */
    async refreshAuthToken(refreshToken) {
        try {
            // Note: This would need to be implemented based on Orange ID's refresh token endpoint
            // For now, we'll just clear the auth and require re-login
            console.log('Token refresh not implemented, clearing auth');
            this.clearSavedAuth();
            return false;
            
        } catch (error) {
            console.error('Token refresh error:', error);
            this.clearSavedAuth();
            return false;
        }
    }
    
    /**
     * Set authenticated state
     */
    setAuthenticatedState(user, token, refreshToken) {
        this.isAuthenticated = true;
        this.user = user;
        this.token = token;
        this.refreshToken = refreshToken;
        
        if (this.onAuthStateChange) {
            this.onAuthStateChange(true, user);
        }
    }
    
    /**
     * Save authentication to localStorage
     */
    saveAuth(user, token, refreshToken) {
        try {
            const authData = {
                user,
                token,
                refreshToken,
                timestamp: Date.now()
            };
            
            localStorage.setItem('warpspace_auth', JSON.stringify(authData));
        } catch (error) {
            console.error('Error saving auth:', error);
        }
    }
    
    /**
     * Clear saved authentication
     */
    clearSavedAuth() {
        try {
            localStorage.removeItem('warpspace_auth');
        } catch (error) {
            console.error('Error clearing saved auth:', error);
        }
    }
    
    /**
     * Sign out user
     */
    async signOut() {
        try {
            // Clear local state
            this.isAuthenticated = false;
            this.user = null;
            this.token = null;
            this.refreshToken = null;
            
            // Clear saved auth
            this.clearSavedAuth();
            
            // Notify state change
            if (this.onAuthStateChange) {
                this.onAuthStateChange(false, null);
            }
            
            console.log('User signed out');
            return true;
            
        } catch (error) {
            console.error('Sign out error:', error);
            return false;
        }
    }
    
    /**
     * Get current user
     */
    getUser() {
        return this.user;
    }
    
    /**
     * Check if user is authenticated
     */
    isUserAuthenticated() {
        return this.isAuthenticated && this.user && this.token;
    }
    
    /**
     * Get authentication token
     */
    getToken() {
        return this.token;
    }
    
    /**
     * Enable debug mode
     */
    enableDebugMode() {
        this.debugMode = true;
        console.log('Debug mode enabled');
    }
    
    /**
     * Disable debug mode
     */
    disableDebugMode() {
        this.debugMode = false;
        console.log('Debug mode disabled');
    }
    
    /**
     * Check if debug mode is enabled
     */
    isDebugMode() {
        return this.debugMode;
    }
    
    /**
     * Generate mock user data for testing
     */
    generateMockUser() {
        const mockUsers = [
            {
                id: 'debug-user-001',
                email: '<EMAIL>',
                name: 'Alice Debug',
                displayName: 'AlicePlayer',
                bio: 'Debug mode user for development testing',
                picture: 'https://via.placeholder.com/80x80/4ecdc4/ffffff?text=AD',
                banner: null,
                ethAddress: '******************************************',
                provider: 'debug',
                createdAt: new Date().toISOString()
            },
            {
                id: 'debug-user-002',
                email: '<EMAIL>',
                name: 'Bob Debug',
                displayName: 'BobTester',
                bio: 'Another debug user for testing purposes',
                picture: 'https://via.placeholder.com/80x80/ff6b6b/ffffff?text=BD',
                banner: null,
                ethAddress: '******************************************',
                provider: 'debug',
                createdAt: new Date().toISOString()
            },
            {
                id: 'debug-user-003',
                email: '<EMAIL>',
                name: 'Charlie Debug',
                displayName: 'CharlieTest',
                bio: 'Third debug user with different profile',
                picture: 'https://via.placeholder.com/80x80/45b7d1/ffffff?text=CD',
                banner: null,
                ethAddress: null,
                provider: 'debug',
                createdAt: new Date().toISOString()
            }
        ];
        
        // Select a random mock user or use the first one
        const selectedUser = mockUsers[Math.floor(Math.random() * mockUsers.length)];
        
        console.log('Generated mock user:', selectedUser);
        return selectedUser;
    }
    
    /**
     * Authenticate with debug bypass (development only)
     */
    async authenticateWithDebugBypass() {
        if (!this.debugMode) {
            console.warn('Debug bypass attempted but debug mode is not enabled');
            throw new Error('Debug bypass is only available in development mode');
        }
        
        console.log('Authenticating with debug bypass...');
        
        try {
            // Generate mock user data
            const mockUser = this.generateMockUser();
            const mockToken = `debug-token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            const mockRefreshToken = `debug-refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            
            // Set authenticated state
            this.setAuthenticatedState(mockUser, mockToken, mockRefreshToken);
            
            // Save to localStorage for persistence during development
            this.saveAuth(mockUser, mockToken, mockRefreshToken);
            
            console.log('Debug authentication successful:', mockUser);
            return true;
            
        } catch (error) {
            console.error('Debug authentication error:', error);
            this.handleAuthError(error);
            return false;
        }
    }
    
    /**
     * Get debug information
     */
    getDebugInfo() {
        return {
            debugMode: this.debugMode,
            isAuthenticated: this.isAuthenticated,
            user: this.user,
            hasToken: !!this.token,
            hasRefreshToken: !!this.refreshToken,
            environment: {
                hostname: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                hasDebugParam: new URLSearchParams(window.location.search).has('debug'),
                userAgent: navigator.userAgent
            },
            localStorage: {
                hasAuth: !!localStorage.getItem('warpspace_auth')
            }
        };
    }
    
    /**
     * Handle authentication errors
     */
    handleAuthError(error) {
        console.error('Authentication error:', error);
        
        if (this.onAuthError) {
            this.onAuthError(error);
        }
    }
    
    /**
     * Set authentication state change callback
     */
    setOnAuthStateChange(callback) {
        this.onAuthStateChange = callback;
    }
    
    /**
     * Set authentication error callback
     */
    setOnAuthError(callback) {
        this.onAuthError = callback;
    }
}