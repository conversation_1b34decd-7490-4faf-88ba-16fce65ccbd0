/**
 * MainMenu - Main menu with Orange ID authentication integration
 * Handles the main menu UI and authentication flow
 */
export class MainMenu {
    constructor(container, authManager) {
        this.container = container;
        this.authManager = authManager;
        
        // UI state
        this.isVisible = false;
        this.currentView = 'login'; // 'login', 'authenticated', 'loading'
        
        // Callbacks
        this.onGameStart = null;
        this.onAuthComplete = null;
        
        // Orange ID widget root
        this.orangeIdRoot = null;
        
        console.log('MainMenu initialized');
    }
    
    /**
     * Initialize the main menu
     */
    async init() {
        try {
            console.log('Initializing MainMenu...');
            
            // Set up auth manager callbacks
            this.authManager.setOnAuthStateChange((isAuthenticated, user) => {
                this.handleAuthStateChange(isAuthenticated, user);
            });
            
            this.authManager.setOnAuthError((error) => {
                this.handleAuthError(error);
            });
            
            // Check initial authentication state
            console.log('Checking authentication state...');
            const isAuthenticated = await this.authManager.init();
            
            console.log('Authentication check result:', isAuthenticated);
            
            if (isAuthenticated) {
                console.log('User is authenticated, showing authenticated view');
                this.showAuthenticatedView();
            } else {
                console.log('User is not authenticated, showing login view');
                this.showLoginView();
            }
            
            console.log('MainMenu initialized successfully');
            
        } catch (error) {
            console.error('MainMenu initialization error:', error);
            this.showErrorView(error);
        }
    }
    
    /**
     * Show the main menu
     */
    show() {
        this.isVisible = true;
        this.container.style.display = 'block';
        this.updateView();
    }
    
    /**
     * Hide the main menu
     */
    hide() {
        this.isVisible = false;
        this.container.style.display = 'none';
    }
    
    /**
     * Update the current view
     */
    updateView() {
        if (!this.isVisible) return;
        
        switch (this.currentView) {
            case 'login':
                this.renderLoginView();
                break;
            case 'authenticated':
                this.renderAuthenticatedView();
                break;
            case 'loading':
                this.renderLoadingView();
                break;
            case 'error':
                this.renderErrorView();
                break;
            default:
                this.renderLoginView();
        }
    }
    
    /**
     * Show login view
     */
    showLoginView() {
        this.currentView = 'login';
        this.updateView();
    }
    
    /**
     * Show authenticated view
     */
    showAuthenticatedView() {
        this.currentView = 'authenticated';
        this.updateView();
    }
    
    /**
     * Show loading view
     */
    showLoadingView() {
        this.currentView = 'loading';
        this.updateView();
    }
    
    /**
     * Show error view
     */
    showErrorView(error) {
        this.currentView = 'error';
        this.errorMessage = error?.message || 'An unknown error occurred';
        this.updateView();
    }
    
    /**
     * Render login view with Orange ID widget
     */
    renderLoginView() {
        console.log('Rendering login view...');
        
        this.container.innerHTML = `
            <div class="main-menu">
                <div class="menu-header">
                    <h1>WarpSpace</h1>
                    <p>Reality Warping Shooter</p>
                </div>
                
                <div class="auth-section">
                    <div id="bedrock-login-widget">
                        <div class="loading-spinner"></div>
                        <p>Loading authentication...</p>
                    </div>
                    ${this.authManager.isDebugMode() ? this.renderDebugSection() : ''}
                </div>
                
                <div class="menu-footer">
                    <p>Powered by Orange ID</p>
                </div>
            </div>
        `;
        
        // Set up debug event listeners if in debug mode
        if (this.authManager.isDebugMode()) {
            this.setupDebugEventListeners();
        }
        
        // Initialize Orange ID widget with a small delay to ensure DOM is ready
        setTimeout(() => {
            this.initializeOrangeIdWidget();
        }, 100);
    }
    
    /**
     * Render authenticated view
     */
    renderAuthenticatedView() {
        const user = this.authManager.getUser();
        
        this.container.innerHTML = `
            <div class="main-menu">
                <div class="menu-header">
                    <h1>WarpSpace</h1>
                    <p>Reality Warping Shooter</p>
                </div>
                
                <div class="user-section">
                    <div class="user-info">
                        ${user?.picture ? `<img src="${user.picture}" alt="Profile" class="user-avatar">` : ''}
                        <h2>Welcome, ${user?.displayName || user?.name || 'Player'}!</h2>
                        <p class="user-email">${user?.email || ''}</p>
                    </div>
                    
                    <div class="menu-actions">
                        <button id="start-game-btn" class="primary-button">Start Game</button>
                        <button id="sign-out-btn" class="secondary-button">Sign Out</button>
                    </div>
                </div>
                
                <div class="menu-footer">
                    <p>Ready to warp reality?</p>
                </div>
            </div>
        `;
        
        // Set up event listeners
        this.setupAuthenticatedEventListeners();
    }
    
    /**
     * Render loading view
     */
    renderLoadingView() {
        this.container.innerHTML = `
            <div class="main-menu">
                <div class="menu-header">
                    <h1>WarpSpace</h1>
                    <p>Reality Warping Shooter</p>
                </div>
                
                <div class="loading-section">
                    <div class="loading-spinner"></div>
                    <p>Authenticating...</p>
                </div>
            </div>
        `;
    }
    
    /**
     * Render error view
     */
    renderErrorView() {
        this.container.innerHTML = `
            <div class="main-menu">
                <div class="menu-header">
                    <h1>WarpSpace</h1>
                    <p>Reality Warping Shooter</p>
                </div>
                
                <div class="error-section">
                    <h2>Authentication Error</h2>
                    <p class="error-message">${this.errorMessage}</p>
                    <button id="retry-auth-btn" class="primary-button">Try Again</button>
                    ${this.authManager.isDebugMode() ? this.renderDebugSection() : ''}
                </div>
            </div>
        `;
        
        // Set up retry button
        const retryBtn = this.container.querySelector('#retry-auth-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.showLoginView();
            });
        }
        
        // Set up debug section if in debug mode
        if (this.authManager.isDebugMode()) {
            this.setupDebugEventListeners();
        }
    }
    
    /**
     * Render debug section for development
     */
    renderDebugSection() {
        const debugInfo = this.authManager.getDebugInfo();
        
        return `
            <div class="debug-section">
                <h3>Debug Mode</h3>
                <p>Development environment detected</p>
                <div class="debug-info">
                    <p><strong>Environment:</strong> ${debugInfo.environment.hostname}:${debugInfo.environment.port}</p>
                    <p><strong>Protocol:</strong> ${debugInfo.environment.protocol}</p>
                    <p><strong>Auth Status:</strong> ${debugInfo.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
                </div>
                <div class="debug-actions">
                    <button id="debug-bypass-btn" class="debug-button">Skip Authentication</button>
                    <button id="debug-clear-btn" class="debug-button">Clear Saved Auth</button>
                    <button id="debug-info-btn" class="debug-button">Show Debug Info</button>
                </div>
                <p class="debug-note">These options are only available in development</p>
            </div>
        `;
    }
    
    /**
     * Initialize Orange ID widget
     */
    initializeOrangeIdWidget() {
        console.log('Initializing Orange ID widget...');
        
        const widgetContainer = this.container.querySelector('#bedrock-login-widget');
        if (!widgetContainer) {
            console.error('Orange ID widget container not found');
            return;
        }
        
        // Check if required libraries are loaded
        if (!window.React || !window.ReactDOM || !window.Bedrock) {
            console.warn('Orange ID libraries not loaded, showing fallback');
            this.showOrangeIdFallback(widgetContainer);
            return;
        }
        
        try {
            console.log('Orange ID libraries detected, initializing widget...');
            
            // Create React root for Orange ID widget
            this.orangeIdRoot = ReactDOM.createRoot(widgetContainer);
            
            // Render Orange ID login panel
            this.orangeIdRoot.render(
                React.createElement(
                    Bedrock.BedrockPassportProvider,
                    this.authManager.config,
                    React.createElement(Bedrock.LoginPanel, {
                        title: 'Sign in to',
                        logo: 'https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg',
                        logoAlt: 'Orange Web3',
                        walletButtonText: 'Connect Wallet',
                        showConnectWallet: false,
                        separatorText: 'OR',
                        
                        features: {
                            enableWalletConnect: false,
                            enableAppleLogin: true,
                            enableGoogleLogin: true,
                            enableEmailLogin: false,
                        },
                        
                        titleClass: 'text-xl font-bold',
                        logoClass: 'ml-2 md:h-8 h-6',
                        panelClass: 'container p-2 md:p-8 rounded-2xl max-w-[480px]',
                        buttonClass: 'hover:border-orange-500',
                        separatorTextClass: 'bg-orange-900 text-gray-500',
                        separatorClass: 'bg-orange-900',
                        linkRowClass: 'justify-center',
                        headerClass: 'justify-center',
                    })
                )
            );
            
            console.log('Orange ID widget initialized successfully');
            
        } catch (error) {
            console.error('Orange ID widget initialization error:', error);
            this.showOrangeIdFallback(widgetContainer);
        }
    }
    
    /**
     * Show fallback when Orange ID widget fails to load
     */
    showOrangeIdFallback(container) {
        console.log('Showing Orange ID fallback');
        
        container.innerHTML = `
            <div class="orange-id-fallback">
                <div class="fallback-message">
                    <h3>Authentication Service Unavailable</h3>
                    <p>The Orange ID authentication service could not be loaded.</p>
                    <p>This may be due to network connectivity issues or service maintenance.</p>
                </div>
                
                ${this.authManager.isDebugMode() ? `
                    <div class="fallback-debug">
                        <p><strong>Debug Mode Available:</strong></p>
                        <p>You can skip authentication for development purposes.</p>
                    </div>
                ` : `
                    <div class="fallback-actions">
                        <button id="retry-orange-id-btn" class="primary-button">Retry Authentication</button>
                        <p class="fallback-note">Please check your internet connection and try again.</p>
                    </div>
                `}
            </div>
        `;
        
        // Set up retry button if not in debug mode
        if (!this.authManager.isDebugMode()) {
            const retryBtn = container.querySelector('#retry-orange-id-btn');
            if (retryBtn) {
                retryBtn.addEventListener('click', () => {
                    console.log('Retrying Orange ID initialization...');
                    this.showLoginView(); // This will re-render and try again
                });
            }
        }
    }
    
    /**
     * Set up event listeners for authenticated view
     */
    setupAuthenticatedEventListeners() {
        const startGameBtn = this.container.querySelector('#start-game-btn');
        const signOutBtn = this.container.querySelector('#sign-out-btn');
        
        if (startGameBtn) {
            startGameBtn.addEventListener('click', () => {
                this.handleStartGame();
            });
        }
        
        if (signOutBtn) {
            signOutBtn.addEventListener('click', async () => {
                await this.handleSignOut();
            });
        }
    }
    
    /**
     * Set up debug event listeners
     */
    setupDebugEventListeners() {
        const debugBypassBtn = this.container.querySelector('#debug-bypass-btn');
        const debugClearBtn = this.container.querySelector('#debug-clear-btn');
        const debugInfoBtn = this.container.querySelector('#debug-info-btn');
        
        if (debugBypassBtn) {
            debugBypassBtn.addEventListener('click', async () => {
                await this.handleDebugBypass();
            });
        }
        
        if (debugClearBtn) {
            debugClearBtn.addEventListener('click', () => {
                this.handleDebugClear();
            });
        }
        
        if (debugInfoBtn) {
            debugInfoBtn.addEventListener('click', () => {
                this.handleDebugInfo();
            });
        }
    }
    
    /**
     * Handle authentication state change
     */
    handleAuthStateChange(isAuthenticated, user) {
        console.log('Auth state changed:', { isAuthenticated, user });
        
        if (isAuthenticated && user) {
            this.showAuthenticatedView();
            
            if (this.onAuthComplete) {
                this.onAuthComplete(user);
            }
        } else {
            this.showLoginView();
        }
    }
    
    /**
     * Handle authentication error
     */
    handleAuthError(error) {
        console.error('Auth error in MainMenu:', error);
        this.showErrorView(error);
    }
    
    /**
     * Handle start game button click
     */
    handleStartGame() {
        console.log('Starting game...');
        
        if (this.onGameStart) {
            this.onGameStart(this.authManager.getUser());
        }
    }
    
    /**
     * Handle sign out button click
     */
    async handleSignOut() {
        try {
            this.showLoadingView();
            await this.authManager.signOut();
            this.showLoginView();
        } catch (error) {
            console.error('Sign out error:', error);
            this.showErrorView(error);
        }
    }
    
    /**
     * Handle debug bypass button click
     */
    async handleDebugBypass() {
        if (!this.authManager.isDebugMode()) {
            console.warn('Debug bypass attempted but debug mode is not enabled');
            return;
        }
        
        console.log('Debug bypass activated');
        
        try {
            // Show loading state
            this.showLoadingView();
            
            // Use the AuthManager's debug bypass functionality
            const success = await this.authManager.authenticateWithDebugBypass();
            
            if (success) {
                console.log('Debug authentication successful');
                // The auth state change callback will handle the UI update
            } else {
                throw new Error('Debug authentication failed');
            }
            
        } catch (error) {
            console.error('Debug bypass error:', error);
            this.showErrorView(error);
        }
    }
    
    /**
     * Handle debug clear button click
     */
    handleDebugClear() {
        if (!this.authManager.isDebugMode()) {
            console.warn('Debug clear attempted but debug mode is not enabled');
            return;
        }
        
        console.log('Clearing saved authentication data...');
        
        try {
            // Clear saved authentication
            this.authManager.clearSavedAuth();
            
            // Sign out if currently authenticated
            if (this.authManager.isUserAuthenticated()) {
                this.authManager.signOut();
            }
            
            // Show login view
            this.showLoginView();
            
            console.log('Authentication data cleared successfully');
            
        } catch (error) {
            console.error('Debug clear error:', error);
            this.showErrorView(error);
        }
    }
    
    /**
     * Handle debug info button click
     */
    handleDebugInfo() {
        if (!this.authManager.isDebugMode()) {
            console.warn('Debug info attempted but debug mode is not enabled');
            return;
        }
        
        console.log('Showing debug information...');
        
        try {
            const debugInfo = this.authManager.getDebugInfo();
            
            // Create a formatted debug info display
            const debugInfoText = JSON.stringify(debugInfo, null, 2);
            
            // Show debug info in console
            console.log('Debug Information:', debugInfo);
            
            // Show debug info in an alert (for quick viewing)
            alert(`Debug Information:\n\n${debugInfoText}`);
            
            // Optionally, you could create a modal or dedicated debug view here
            
        } catch (error) {
            console.error('Debug info error:', error);
            alert(`Error retrieving debug info: ${error.message}`);
        }
    }
    
    /**
     * Set game start callback
     */
    setOnGameStart(callback) {
        this.onGameStart = callback;
    }
    
    /**
     * Set auth complete callback
     */
    setOnAuthComplete(callback) {
        this.onAuthComplete = callback;
    }
    
    /**
     * Cleanup resources
     */
    destroy() {
        if (this.orangeIdRoot) {
            this.orangeIdRoot.unmount();
            this.orangeIdRoot = null;
        }
        
        this.container.innerHTML = '';
        console.log('MainMenu destroyed');
    }
}