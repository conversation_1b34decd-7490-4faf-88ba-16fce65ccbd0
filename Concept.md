1. WarpSpace (Vertical Scrolling Shooter)
Your ship ascends through procedurally generated dimensional rifts and encounters a Genie between levels. Players win "WISH" (our token) to wish for reality to warp when the Genie appears. Between levels, players or AI bosses trigger reality warps that completely reshape the battlefield - turning asteroid fields into crystal mazes, space into underwater caverns, or nebulae into volcanic landscapes. Each transformation comes with  strategic advantages/disadvantages. The Fal.ai generator creates these battlefields based on user input or predefined prompts for the AI bosses, with JSON attributes defining gravity, enemy spawn patterns, obstacle density, etc. Utilize Orange ID for logins (with a debug button to bypass logins for local development/testing) and Orange SDK to save player progress/level completions.

WISH - Refined Token Economy Design
Core Token Flow (Deflationary)

Token Income: Players earn small amounts for completing levels (paid from our 10% supply via hot wallet). The faster they complete a level, the higher their score.
Token Spending: Players must spend tokens to activate Reality Warp powers
Net Positive: Warp and power up costs exceed level completion rewards, creating token sink.

Gameplay Loop
Standard Play: Ship ascends through levels, fighting pre-generated enemy waves in default space environments. Players earn modest token rewards for completion.
Risk/Reward Decision: Before beginning the next level, players can spend tokens to buy up to three power ups (extra wingman, extra life, spread pattern ammo - up to one of each), and/or warp reality in their favor. AI bosses automatically warp reality against them before the level.
Reality Warp Mechanics
Player-Initiated Warps (Token Cost):

Transform battlefield to disadvantageous terrain for current enemies
Water enemies struggle in lava fields
Ice enemies melt in desert heat
Flying enemies crash in dense forest canopies
Cost: Moderate token fee, but easier progression = higher completion bonuses

AI Boss Warps:

AI changes terrain to spawn appropriate enemy types
Underwater → water enemies appear
Volcanic → fire enemies spawn
Crystal caverns → energy-based foes
Players face harder challenge but can counter-warp for tokens

High-Stakes Motivation
Daily/Weekly Raffle: Top performers can win token jackpots (funded by the fees from all players, equal to half the net profit). Creates competitive incentive to spend tokens for better performance. E.g. If players spend 1500 wish tokens this week, and we distributed 500 WISH tokens out as players played the game, the top ten players would be entered into a raffle, and then a random selection will select 3 players for "Gold Jackpot", "Silver Jackpot", and "Bronze Jackpot". These winners would collectively recieve 500 WISH tokens, with the Gold Jackpot being 250 WISH, Silver being 150 WISH, and Bronze being 100 WISH.
Technical Implementation

Fal.ai: Generates battlefield images only based on user input (or predefined prompts for AI bosses).
Separate LLM: Analyzes user input and outputs JSON with enemy compatibility scores, terrain advantages, environmental effects
Pre-built Enemy Sets: Water, fire, air, earth, crystal, shadow types - deployed based on JSON analysis
Orange SDK: Tracks completion stats for token payouts
