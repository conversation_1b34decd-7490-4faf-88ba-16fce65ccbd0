{"version": 3, "file": "index-CXHmaMUw.js", "sources": ["../../src/utils/Vector2.js", "../../src/input/InputManager.js", "../../src/utils/GameObject.js", "../../src/entities/Projectile.js", "../../src/systems/WeaponSystem.js", "../../src/entities/PlayerShip.js", "../../src/utils/ObjectPool.js", "../../src/utils/GameObjectManager.js", "../../src/engine/GameEngine.js", "../../src/main.js"], "sourcesContent": ["/**\n * Vector2 utility class for 2D math operations\n */\nexport class Vector2 {\n    constructor(x = 0, y = 0) {\n        this.x = x;\n        this.y = y;\n    }\n    \n    // Static factory methods\n    static zero() {\n        return new Vector2(0, 0);\n    }\n    \n    static one() {\n        return new Vector2(1, 1);\n    }\n    \n    static up() {\n        return new Vector2(0, -1);\n    }\n    \n    static down() {\n        return new Vector2(0, 1);\n    }\n    \n    static left() {\n        return new Vector2(-1, 0);\n    }\n    \n    static right() {\n        return new Vector2(1, 0);\n    }\n    \n    // Vector operations\n    add(other) {\n        return new Vector2(this.x + other.x, this.y + other.y);\n    }\n    \n    subtract(other) {\n        return new Vector2(this.x - other.x, this.y - other.y);\n    }\n    \n    multiply(scalar) {\n        return new Vector2(this.x * scalar, this.y * scalar);\n    }\n    \n    divide(scalar) {\n        if (scalar === 0) throw new Error('Division by zero');\n        return new Vector2(this.x / scalar, this.y / scalar);\n    }\n    \n    // Magnitude and normalization\n    magnitude() {\n        return Math.sqrt(this.x * this.x + this.y * this.y);\n    }\n    \n    normalize() {\n        const mag = this.magnitude();\n        if (mag === 0) return Vector2.zero();\n        return this.divide(mag);\n    }\n    \n    // Distance calculations\n    distance(other) {\n        return this.subtract(other).magnitude();\n    }\n    \n    // Dot product\n    dot(other) {\n        return this.x * other.x + this.y * other.y;\n    }\n    \n    // In-place operations (modify this vector)\n    addInPlace(other) {\n        this.x += other.x;\n        this.y += other.y;\n        return this;\n    }\n    \n    subtractInPlace(other) {\n        this.x -= other.x;\n        this.y -= other.y;\n        return this;\n    }\n    \n    multiplyInPlace(scalar) {\n        this.x *= scalar;\n        this.y *= scalar;\n        return this;\n    }\n    \n    normalizeInPlace() {\n        const mag = this.magnitude();\n        if (mag > 0) {\n            this.x /= mag;\n            this.y /= mag;\n        }\n        return this;\n    }\n    \n    // Set values\n    set(x, y) {\n        this.x = x;\n        this.y = y;\n        return this;\n    }\n    \n    setFromVector(other) {\n        this.x = other.x;\n        this.y = other.y;\n        return this;\n    }\n    \n    // Angle operations\n    angle() {\n        return Math.atan2(this.y, this.x);\n    }\n    \n    static fromAngle(angle, magnitude = 1) {\n        return new Vector2(\n            Math.cos(angle) * magnitude,\n            Math.sin(angle) * magnitude\n        );\n    }\n    \n    // Rotation\n    rotate(angle) {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n        const newX = this.x * cos - this.y * sin;\n        const newY = this.x * sin + this.y * cos;\n        return new Vector2(newX, newY);\n    }\n    \n    rotateInPlace(angle) {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n        const newX = this.x * cos - this.y * sin;\n        const newY = this.x * sin + this.y * cos;\n        this.x = newX;\n        this.y = newY;\n        return this;\n    }\n    \n    // Perpendicular vector\n    perpendicular() {\n        return new Vector2(-this.y, this.x);\n    }\n    \n    // Utility methods\n    clone() {\n        return new Vector2(this.x, this.y);\n    }\n    \n    equals(other, tolerance = 0) {\n        if (tolerance === 0) {\n            return this.x === other.x && this.y === other.y;\n        }\n        return Math.abs(this.x - other.x) <= tolerance && \n               Math.abs(this.y - other.y) <= tolerance;\n    }\n    \n    toString() {\n        return `Vector2(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`;\n    }\n}", "import { Vector2 } from '../utils/Vector2.js';\n\n/**\n * InputManager - <PERSON><PERSON> keyboard, mouse, and touch input\n * Provides unified input interface for ship controls\n */\nexport class InputManager {\n    constructor(canvas) {\n        this.canvas = canvas;\n        \n        // Input state tracking\n        this.keys = new Map();\n        this.keysPressed = new Map();\n        this.keysReleased = new Map();\n        \n        this.mousePosition = new Vector2(0, 0);\n        this.mouseButtons = new Map();\n        this.mousePressed = new Map();\n        this.mouseReleased = new Map();\n        \n        this.touches = new Map();\n        this.touchStarted = new Map();\n        this.touchEnded = new Map();\n        \n        // Input mapping configuration\n        this.keyMappings = new Map();\n        this.setupDefaultMappings();\n        \n        // Mobile/touch support\n        this.isTouchDevice = 'ontouchstart' in window;\n        this.virtualJoystick = null;\n        \n        // Bind event handlers\n        this.handleKeyDown = this.handleKeyDown.bind(this);\n        this.handleKeyUp = this.handleKeyUp.bind(this);\n        this.handleMouseDown = this.handleMouseDown.bind(this);\n        this.handleMouseUp = this.handleMouseUp.bind(this);\n        this.handleMouseMove = this.handleMouseMove.bind(this);\n        this.handleTouchStart = this.handleTouchStart.bind(this);\n        this.handleTouchEnd = this.handleTouchEnd.bind(this);\n        this.handleTouchMove = this.handleTouchMove.bind(this);\n        \n        // Initialize event listeners\n        this.init();\n    }\n    \n    init() {\n        // Keyboard events\n        document.addEventListener('keydown', this.handleKeyDown);\n        document.addEventListener('keyup', this.handleKeyUp);\n        \n        // Mouse events\n        this.canvas.addEventListener('mousedown', this.handleMouseDown);\n        this.canvas.addEventListener('mouseup', this.handleMouseUp);\n        this.canvas.addEventListener('mousemove', this.handleMouseMove);\n        \n        // Touch events\n        this.canvas.addEventListener('touchstart', this.handleTouchStart, { passive: false });\n        this.canvas.addEventListener('touchend', this.handleTouchEnd, { passive: false });\n        this.canvas.addEventListener('touchmove', this.handleTouchMove, { passive: false });\n        \n        // Prevent context menu on canvas\n        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());\n        \n        // Initialize virtual joystick for touch devices\n        if (this.isTouchDevice) {\n            this.initVirtualJoystick();\n        }\n        \n        console.log('InputManager initialized');\n    }\n    \n    setupDefaultMappings() {\n        // Movement controls\n        this.keyMappings.set('moveUp', ['ArrowUp', 'KeyW']);\n        this.keyMappings.set('moveDown', ['ArrowDown', 'KeyS']);\n        this.keyMappings.set('moveLeft', ['ArrowLeft', 'KeyA']);\n        this.keyMappings.set('moveRight', ['ArrowRight', 'KeyD']);\n        \n        // Action controls\n        this.keyMappings.set('fire', ['Space', 'Enter']);\n        this.keyMappings.set('pause', ['Escape', 'KeyP']);\n        this.keyMappings.set('interact', ['KeyE', 'KeyF']);\n        \n        // Debug controls\n        this.keyMappings.set('debug', ['F1']);\n    }\n    \n    // Keyboard event handlers\n    handleKeyDown(event) {\n        const key = event.code;\n        \n        if (!this.keys.get(key)) {\n            this.keysPressed.set(key, true);\n        }\n        \n        this.keys.set(key, true);\n        \n        // Prevent default for game controls\n        if (this.isGameKey(key)) {\n            event.preventDefault();\n        }\n    }\n    \n    handleKeyUp(event) {\n        const key = event.code;\n        this.keys.set(key, false);\n        this.keysReleased.set(key, true);\n        \n        if (this.isGameKey(key)) {\n            event.preventDefault();\n        }\n    }\n    \n    // Mouse event handlers\n    handleMouseDown(event) {\n        const button = event.button;\n        \n        if (!this.mouseButtons.get(button)) {\n            this.mousePressed.set(button, true);\n        }\n        \n        this.mouseButtons.set(button, true);\n        this.updateMousePosition(event);\n        \n        event.preventDefault();\n    }\n    \n    handleMouseUp(event) {\n        const button = event.button;\n        this.mouseButtons.set(button, false);\n        this.mouseReleased.set(button, true);\n        this.updateMousePosition(event);\n        \n        event.preventDefault();\n    }\n    \n    handleMouseMove(event) {\n        this.updateMousePosition(event);\n    }\n    \n    updateMousePosition(event) {\n        const rect = this.canvas.getBoundingClientRect();\n        this.mousePosition.set(\n            event.clientX - rect.left,\n            event.clientY - rect.top\n        );\n    }\n    \n    // Touch event handlers\n    handleTouchStart(event) {\n        event.preventDefault();\n        \n        for (const touch of event.changedTouches) {\n            const touchPos = this.getTouchPosition(touch);\n            this.touches.set(touch.identifier, touchPos);\n            this.touchStarted.set(touch.identifier, touchPos.clone());\n            \n            // Update virtual joystick\n            if (this.virtualJoystick) {\n                this.virtualJoystick.handleTouchStart(touch.identifier, touchPos);\n            }\n        }\n    }\n    \n    handleTouchEnd(event) {\n        event.preventDefault();\n        \n        for (const touch of event.changedTouches) {\n            const touchPos = this.getTouchPosition(touch);\n            this.touchEnded.set(touch.identifier, touchPos);\n            this.touches.delete(touch.identifier);\n            \n            // Update virtual joystick\n            if (this.virtualJoystick) {\n                this.virtualJoystick.handleTouchEnd(touch.identifier);\n            }\n        }\n    }\n    \n    handleTouchMove(event) {\n        event.preventDefault();\n        \n        for (const touch of event.changedTouches) {\n            const touchPos = this.getTouchPosition(touch);\n            this.touches.set(touch.identifier, touchPos);\n            \n            // Update virtual joystick\n            if (this.virtualJoystick) {\n                this.virtualJoystick.handleTouchMove(touch.identifier, touchPos);\n            }\n        }\n    }\n    \n    getTouchPosition(touch) {\n        const rect = this.canvas.getBoundingClientRect();\n        return new Vector2(\n            touch.clientX - rect.left,\n            touch.clientY - rect.top\n        );\n    }\n    \n    // Input query methods\n    isKeyDown(key) {\n        return this.keys.get(key) || false;\n    }\n    \n    isKeyPressed(key) {\n        return this.keysPressed.get(key) || false;\n    }\n    \n    isKeyReleased(key) {\n        return this.keysReleased.get(key) || false;\n    }\n    \n    isMouseDown(button = 0) {\n        return this.mouseButtons.get(button) || false;\n    }\n    \n    isMousePressed(button = 0) {\n        return this.mousePressed.get(button) || false;\n    }\n    \n    isMouseReleased(button = 0) {\n        return this.mouseReleased.get(button) || false;\n    }\n    \n    // Action-based input queries\n    isActionDown(action) {\n        const keys = this.keyMappings.get(action);\n        if (!keys) return false;\n        \n        return keys.some(key => this.isKeyDown(key));\n    }\n    \n    isActionPressed(action) {\n        const keys = this.keyMappings.get(action);\n        if (!keys) return false;\n        \n        return keys.some(key => this.isKeyPressed(key));\n    }\n    \n    isActionReleased(action) {\n        const keys = this.keyMappings.get(action);\n        if (!keys) return false;\n        \n        return keys.some(key => this.isKeyReleased(key));\n    }\n    \n    // Movement input helpers\n    getMovementVector() {\n        const movement = new Vector2(0, 0);\n        \n        if (this.isActionDown('moveLeft')) movement.x -= 1;\n        if (this.isActionDown('moveRight')) movement.x += 1;\n        if (this.isActionDown('moveUp')) movement.y -= 1;\n        if (this.isActionDown('moveDown')) movement.y += 1;\n        \n        // Add virtual joystick input for touch devices\n        if (this.virtualJoystick && this.virtualJoystick.isActive()) {\n            const joystickInput = this.virtualJoystick.getInput();\n            movement.addInPlace(joystickInput);\n        }\n        \n        // Normalize diagonal movement\n        if (movement.magnitude() > 1) {\n            movement.normalizeInPlace();\n        }\n        \n        return movement;\n    }\n    \n    // Key mapping management\n    setKeyMapping(action, keys) {\n        this.keyMappings.set(action, Array.isArray(keys) ? keys : [keys]);\n    }\n    \n    addKeyMapping(action, key) {\n        const existing = this.keyMappings.get(action) || [];\n        existing.push(key);\n        this.keyMappings.set(action, existing);\n    }\n    \n    removeKeyMapping(action, key) {\n        const existing = this.keyMappings.get(action) || [];\n        const filtered = existing.filter(k => k !== key);\n        this.keyMappings.set(action, filtered);\n    }\n    \n    // Utility methods\n    isGameKey(key) {\n        for (const keys of this.keyMappings.values()) {\n            if (keys.includes(key)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    \n    // Virtual joystick for touch devices\n    initVirtualJoystick() {\n        this.virtualJoystick = new VirtualJoystick(this.canvas);\n    }\n    \n    // Update method - call once per frame\n    update() {\n        // Clear frame-specific input states\n        this.keysPressed.clear();\n        this.keysReleased.clear();\n        this.mousePressed.clear();\n        this.mouseReleased.clear();\n        this.touchStarted.clear();\n        this.touchEnded.clear();\n        \n        // Update virtual joystick\n        if (this.virtualJoystick) {\n            this.virtualJoystick.update();\n        }\n    }\n    \n    // Render debug info and virtual controls\n    render(ctx) {\n        if (this.virtualJoystick) {\n            this.virtualJoystick.render(ctx);\n        }\n    }\n    \n    // Cleanup\n    destroy() {\n        document.removeEventListener('keydown', this.handleKeyDown);\n        document.removeEventListener('keyup', this.handleKeyUp);\n        \n        this.canvas.removeEventListener('mousedown', this.handleMouseDown);\n        this.canvas.removeEventListener('mouseup', this.handleMouseUp);\n        this.canvas.removeEventListener('mousemove', this.handleMouseMove);\n        \n        this.canvas.removeEventListener('touchstart', this.handleTouchStart);\n        this.canvas.removeEventListener('touchend', this.handleTouchEnd);\n        this.canvas.removeEventListener('touchmove', this.handleTouchMove);\n        \n        if (this.virtualJoystick) {\n            this.virtualJoystick.destroy();\n        }\n        \n        console.log('InputManager destroyed');\n    }\n}\n\n/**\n * Virtual Joystick for touch devices\n */\nclass VirtualJoystick {\n    constructor(canvas) {\n        this.canvas = canvas;\n        this.active = false;\n        this.touchId = null;\n        \n        // Joystick properties\n        this.center = new Vector2(100, canvas.height - 100);\n        this.knobPosition = new Vector2(100, canvas.height - 100);\n        this.maxDistance = 50;\n        this.deadZone = 0.1;\n        \n        // Visual properties\n        this.baseRadius = 60;\n        this.knobRadius = 25;\n        this.baseColor = 'rgba(255, 255, 255, 0.3)';\n        this.knobColor = 'rgba(255, 255, 255, 0.7)';\n    }\n    \n    handleTouchStart(touchId, position) {\n        // Check if touch is within joystick area\n        const distance = position.distance(this.center);\n        if (distance <= this.baseRadius) {\n            this.active = true;\n            this.touchId = touchId;\n            this.knobPosition.setFromVector(position);\n            this.clampKnobPosition();\n        }\n    }\n    \n    handleTouchMove(touchId, position) {\n        if (this.active && this.touchId === touchId) {\n            this.knobPosition.setFromVector(position);\n            this.clampKnobPosition();\n        }\n    }\n    \n    handleTouchEnd(touchId) {\n        if (this.active && this.touchId === touchId) {\n            this.active = false;\n            this.touchId = null;\n            this.knobPosition.setFromVector(this.center);\n        }\n    }\n    \n    clampKnobPosition() {\n        const offset = this.knobPosition.subtract(this.center);\n        if (offset.magnitude() > this.maxDistance) {\n            offset.normalizeInPlace().multiplyInPlace(this.maxDistance);\n            this.knobPosition = this.center.add(offset);\n        }\n    }\n    \n    getInput() {\n        if (!this.active) return new Vector2(0, 0);\n        \n        const offset = this.knobPosition.subtract(this.center);\n        const magnitude = offset.magnitude() / this.maxDistance;\n        \n        if (magnitude < this.deadZone) {\n            return new Vector2(0, 0);\n        }\n        \n        return offset.normalize().multiply(magnitude);\n    }\n    \n    isActive() {\n        return this.active;\n    }\n    \n    update() {\n        // Update joystick position based on screen size changes\n        this.center.set(100, this.canvas.height - 100);\n        if (!this.active) {\n            this.knobPosition.setFromVector(this.center);\n        }\n    }\n    \n    render(ctx) {\n        // Draw base circle\n        ctx.save();\n        ctx.fillStyle = this.baseColor;\n        ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';\n        ctx.lineWidth = 2;\n        \n        ctx.beginPath();\n        ctx.arc(this.center.x, this.center.y, this.baseRadius, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        \n        // Draw knob\n        ctx.fillStyle = this.knobColor;\n        ctx.beginPath();\n        ctx.arc(this.knobPosition.x, this.knobPosition.y, this.knobRadius, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        \n        ctx.restore();\n    }\n    \n    destroy() {\n        // Cleanup if needed\n    }\n}", "import { Vector2 } from './Vector2.js';\n\n/**\n * Base GameObject class for all game entities\n * Provides common functionality for position, movement, and lifecycle\n */\nexport class GameObject {\n    constructor(x = 0, y = 0) {\n        this.position = new Vector2(x, y);\n        this.velocity = new Vector2(0, 0);\n        this.acceleration = new Vector2(0, 0);\n        \n        // Transform properties\n        this.rotation = 0;\n        this.scale = new Vector2(1, 1);\n        \n        // State properties\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        \n        // Collision properties\n        this.collisionRadius = 0;\n        this.collisionBounds = { x: 0, y: 0, width: 0, height: 0 };\n        \n        // Unique identifier\n        this.id = GameObject.generateId();\n        \n        // Tags for categorization\n        this.tags = new Set();\n    }\n    \n    // Static ID generator\n    static idCounter = 0;\n    static generateId() {\n        return ++GameObject.idCounter;\n    }\n    \n    // Update method - override in subclasses\n    update(deltaTime) {\n        if (!this.active) return;\n        \n        // Apply physics\n        this.velocity.addInPlace(this.acceleration.multiply(deltaTime / 1000));\n        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n    }\n    \n    // Render method - override in subclasses\n    render(ctx, interpolation = 0) {\n        if (!this.visible) return;\n        \n        // Interpolated position for smooth rendering\n        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));\n        \n        // Basic debug rendering (override in subclasses)\n        ctx.save();\n        ctx.translate(renderPos.x, renderPos.y);\n        ctx.rotate(this.rotation);\n        ctx.scale(this.scale.x, this.scale.y);\n        \n        // Draw collision bounds for debugging\n        if (this.collisionRadius > 0) {\n            ctx.strokeStyle = '#ff0000';\n            ctx.beginPath();\n            ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);\n            ctx.stroke();\n        }\n        \n        ctx.restore();\n    }\n    \n    // Collision detection methods\n    updateCollisionBounds() {\n        this.collisionBounds.x = this.position.x - this.collisionRadius;\n        this.collisionBounds.y = this.position.y - this.collisionRadius;\n        this.collisionBounds.width = this.collisionRadius * 2;\n        this.collisionBounds.height = this.collisionRadius * 2;\n    }\n    \n    // Check collision with another GameObject\n    collidesWith(other) {\n        if (!this.active || !other.active) return false;\n        \n        // Circle collision detection\n        if (this.collisionRadius > 0 && other.collisionRadius > 0) {\n            const distance = this.position.distance(other.position);\n            return distance < (this.collisionRadius + other.collisionRadius);\n        }\n        \n        return false;\n    }\n    \n    // Lifecycle methods\n    destroy() {\n        this.destroyed = true;\n        this.active = false;\n        this.visible = false;\n    }\n    \n    reset() {\n        this.position.set(0, 0);\n        this.velocity.set(0, 0);\n        this.acceleration.set(0, 0);\n        this.rotation = 0;\n        this.scale.set(1, 1);\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        this.tags.clear();\n    }\n    \n    // Tag management\n    addTag(tag) {\n        this.tags.add(tag);\n    }\n    \n    removeTag(tag) {\n        this.tags.delete(tag);\n    }\n    \n    hasTag(tag) {\n        return this.tags.has(tag);\n    }\n    \n    // Utility methods\n    distanceTo(other) {\n        return this.position.distance(other.position);\n    }\n    \n    directionTo(other) {\n        return other.position.subtract(this.position).normalize();\n    }\n    \n    lookAt(target) {\n        const direction = this.directionTo(target);\n        this.rotation = direction.angle();\n    }\n    \n    // Movement helpers\n    moveTowards(target, speed, deltaTime) {\n        const direction = this.directionTo(target);\n        const movement = direction.multiply(speed * deltaTime / 1000);\n        this.position.addInPlace(movement);\n    }\n    \n    applyForce(force) {\n        this.acceleration.addInPlace(force);\n    }\n    \n    // Boundary checking\n    isOutOfBounds(bounds) {\n        return this.position.x < bounds.left || \n               this.position.x > bounds.right ||\n               this.position.y < bounds.top || \n               this.position.y > bounds.bottom;\n    }\n    \n    wrapAroundBounds(bounds) {\n        if (this.position.x < bounds.left) this.position.x = bounds.right;\n        if (this.position.x > bounds.right) this.position.x = bounds.left;\n        if (this.position.y < bounds.top) this.position.y = bounds.bottom;\n        if (this.position.y > bounds.bottom) this.position.y = bounds.top;\n    }\n    \n    clampToBounds(bounds) {\n        this.position.x = Math.max(bounds.left, Math.min(bounds.right, this.position.x));\n        this.position.y = Math.max(bounds.top, Math.min(bounds.bottom, this.position.y));\n    }\n}", "import { GameObject } from '../utils/GameObject.js';\nimport { Vector2 } from '../utils/Vector2.js';\n\n/**\n * Projectile class - Represents bullets/projectiles fired by ships\n * Handles movement, collision detection, and visual effects\n */\nexport class Projectile extends GameObject {\n    constructor(x = 0, y = 0) {\n        super(x, y);\n        \n        // Projectile properties\n        this.speed = 600; // pixels per second\n        this.damage = 1;\n        this.lifetime = 3000; // milliseconds\n        this.age = 0;\n        \n        // Visual properties\n        this.width = 4;\n        this.height = 12;\n        this.collisionRadius = 3;\n        this.color = '#FFD700'; // Gold color\n        this.trailColor = '#FFA500'; // Orange trail\n        \n        // Trail effect properties\n        this.trailPositions = [];\n        this.maxTrailLength = 8;\n        this.trailFadeRate = 0.8;\n        \n        // Projectile type and owner\n        this.type = 'player'; // 'player' or 'enemy'\n        this.owner = null;\n        \n        // Add projectile tag\n        this.addTag('projectile');\n        \n        // Initialize as inactive\n        this.active = false;\n        this.visible = false;\n    }\n    \n    /**\n     * Initialize projectile with starting parameters\n     * @param {Vector2} position - Starting position\n     * @param {Vector2} direction - Direction vector (normalized)\n     * @param {number} speed - Projectile speed\n     * @param {string} type - Projectile type ('player' or 'enemy')\n     * @param {GameObject} owner - Object that fired this projectile\n     */\n    initialize(position, direction, speed = 600, type = 'player', owner = null) {\n        this.position.setFromVector(position);\n        this.velocity = direction.normalize().multiply(speed);\n        this.speed = speed;\n        this.type = type;\n        this.owner = owner;\n        this.age = 0;\n        \n        // Clear trail\n        this.trailPositions = [];\n        \n        // Set visual properties based on type\n        this.setupVisualsByType();\n        \n        // Activate projectile\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        \n        // Add appropriate tags\n        this.tags.clear();\n        this.addTag('projectile');\n        this.addTag(type + 'Projectile');\n        \n        return this;\n    }\n    \n    /**\n     * Set up visual properties based on projectile type\n     */\n    setupVisualsByType() {\n        switch (this.type) {\n            case 'player':\n                this.color = '#FFD700'; // Gold\n                this.trailColor = '#FFA500'; // Orange\n                this.width = 4;\n                this.height = 12;\n                break;\n            case 'enemy':\n                this.color = '#FF4444'; // Red\n                this.trailColor = '#FF8888'; // Light red\n                this.width = 3;\n                this.height = 8;\n                break;\n            default:\n                this.color = '#FFFFFF'; // White\n                this.trailColor = '#CCCCCC'; // Light gray\n                break;\n        }\n    }\n    \n    /**\n     * Update projectile movement and lifetime\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    update(deltaTime) {\n        if (!this.active) return;\n        \n        // Update age\n        this.age += deltaTime;\n        \n        // Check lifetime\n        if (this.age >= this.lifetime) {\n            this.destroy();\n            return;\n        }\n        \n        // Store current position for trail effect\n        this.updateTrail();\n        \n        // Update position using parent class physics\n        super.update(deltaTime);\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n    }\n    \n    /**\n     * Update trail effect positions\n     */\n    updateTrail() {\n        // Add current position to trail\n        this.trailPositions.unshift(this.position.clone());\n        \n        // Limit trail length\n        if (this.trailPositions.length > this.maxTrailLength) {\n            this.trailPositions.pop();\n        }\n    }\n    \n    /**\n     * Render projectile with trail effects\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor for smooth rendering\n     */\n    render(ctx, interpolation = 0) {\n        if (!this.visible) return;\n        \n        // Calculate interpolated position\n        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));\n        \n        ctx.save();\n        \n        // Draw trail effect\n        this.renderTrail(ctx, interpolation);\n        \n        // Draw main projectile\n        this.renderProjectile(ctx, renderPos);\n        \n        // Draw debug info if enabled\n        if (window.DEBUG_MODE) {\n            this.renderDebugInfo(ctx, renderPos);\n        }\n        \n        ctx.restore();\n    }\n    \n    /**\n     * Render projectile trail effect\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor\n     */\n    renderTrail(ctx, interpolation) {\n        if (this.trailPositions.length < 2) return;\n        \n        ctx.strokeStyle = this.trailColor;\n        ctx.lineWidth = 2;\n        ctx.lineCap = 'round';\n        \n        // Draw trail segments with fading alpha\n        for (let i = 0; i < this.trailPositions.length - 1; i++) {\n            const alpha = Math.pow(this.trailFadeRate, i);\n            const currentPos = this.trailPositions[i];\n            const nextPos = this.trailPositions[i + 1];\n            \n            // Apply interpolation to the most recent trail segment\n            let renderCurrentPos = currentPos;\n            if (i === 0) {\n                const interpolatedOffset = this.velocity.multiply(interpolation / 1000);\n                renderCurrentPos = currentPos.add(interpolatedOffset);\n            }\n            \n            ctx.globalAlpha = alpha;\n            ctx.beginPath();\n            ctx.moveTo(renderCurrentPos.x, renderCurrentPos.y);\n            ctx.lineTo(nextPos.x, nextPos.y);\n            ctx.stroke();\n        }\n        \n        ctx.globalAlpha = 1.0;\n    }\n    \n    /**\n     * Render the main projectile body\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {Vector2} renderPos - Interpolated render position\n     */\n    renderProjectile(ctx, renderPos) {\n        ctx.translate(renderPos.x, renderPos.y);\n        \n        // Main projectile body - simple rectangle for now\n        ctx.fillStyle = this.color;\n        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);\n        \n        // Add a bright center\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillRect(-1, -this.height / 2, 2, this.height);\n    }\n    \n    /**\n     * Draw player projectile shape\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawPlayerProjectile(ctx) {\n        // Elongated diamond shape\n        ctx.beginPath();\n        ctx.moveTo(0, -this.height / 2); // Top point\n        ctx.lineTo(this.width / 2, 0); // Right point\n        ctx.lineTo(0, this.height / 2); // Bottom point\n        ctx.lineTo(-this.width / 2, 0); // Left point\n        ctx.closePath();\n        ctx.fill();\n        ctx.stroke();\n        \n        // Inner glow effect\n        ctx.fillStyle = this.lightenColor(this.color, 0.3);\n        ctx.beginPath();\n        ctx.moveTo(0, -this.height / 4);\n        ctx.lineTo(this.width / 4, 0);\n        ctx.lineTo(0, this.height / 4);\n        ctx.lineTo(-this.width / 4, 0);\n        ctx.closePath();\n        ctx.fill();\n    }\n    \n    /**\n     * Draw enemy projectile shape\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawEnemyProjectile(ctx) {\n        // Simple oval shape\n        ctx.beginPath();\n        ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        \n        // Inner highlight\n        ctx.fillStyle = this.lightenColor(this.color, 0.2);\n        ctx.beginPath();\n        ctx.ellipse(0, -this.height / 6, this.width / 4, this.height / 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Render debug information\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {Vector2} renderPos - Render position\n     */\n    renderDebugInfo(ctx, renderPos) {\n        ctx.resetTransform();\n        \n        // Collision circle\n        ctx.strokeStyle = '#FF0000';\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.arc(renderPos.x, renderPos.y, this.collisionRadius, 0, Math.PI * 2);\n        ctx.stroke();\n        \n        // Velocity vector\n        ctx.strokeStyle = '#00FF00';\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.moveTo(renderPos.x, renderPos.y);\n        const velocityEnd = renderPos.add(this.velocity.multiply(0.05)); // Scale for visibility\n        ctx.lineTo(velocityEnd.x, velocityEnd.y);\n        ctx.stroke();\n        \n        // Age indicator\n        ctx.fillStyle = '#FFFF00';\n        ctx.font = '10px Arial';\n        ctx.fillText(`${Math.floor(this.age)}ms`, renderPos.x + 10, renderPos.y - 10);\n    }\n    \n    /**\n     * Check if projectile is out of bounds\n     * @param {object} bounds - Boundary object with left, right, top, bottom properties\n     * @returns {boolean} True if out of bounds\n     */\n    isOutOfBounds(bounds) {\n        const margin = Math.max(this.width, this.height);\n        return this.position.x < bounds.left - margin ||\n               this.position.x > bounds.right + margin ||\n               this.position.y < bounds.top - margin ||\n               this.position.y > bounds.bottom + margin;\n    }\n    \n    /**\n     * Handle collision with another object\n     * @param {GameObject} other - Object that was hit\n     */\n    onCollision(other) {\n        // Override in subclasses for specific collision behavior\n        this.destroy();\n    }\n    \n    /**\n     * Reset projectile to inactive state (for object pooling)\n     */\n    reset() {\n        super.reset();\n        this.age = 0;\n        this.trailPositions = [];\n        this.type = 'player';\n        this.owner = null;\n        this.speed = 600;\n        this.damage = 1;\n        this.lifetime = 3000;\n    }\n    \n    /**\n     * Utility function to darken a color\n     * @param {string} color - Hex color string\n     * @param {number} factor - Darkening factor (0-1)\n     * @returns {string} Darkened color\n     */\n    darkenColor(color, factor) {\n        // Simple darkening - in a real implementation you might want more sophisticated color manipulation\n        const hex = color.replace('#', '');\n        const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));\n        const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));\n        const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));\n        return `rgb(${r}, ${g}, ${b})`;\n    }\n    \n    /**\n     * Utility function to lighten a color\n     * @param {string} color - Hex color string\n     * @param {number} factor - Lightening factor (0-1)\n     * @returns {string} Lightened color\n     */\n    lightenColor(color, factor) {\n        const hex = color.replace('#', '');\n        const r = Math.min(255, Math.floor(parseInt(hex.substr(0, 2), 16) * (1 + factor)));\n        const g = Math.min(255, Math.floor(parseInt(hex.substr(2, 2), 16) * (1 + factor)));\n        const b = Math.min(255, Math.floor(parseInt(hex.substr(4, 2), 16) * (1 + factor)));\n        return `rgb(${r}, ${g}, ${b})`;\n    }\n}", "import { Vector2 } from '../utils/Vector2.js';\nimport { Projectile } from '../entities/Projectile.js';\n\n/**\n * WeaponSystem class - Manages weapon firing, projectile spawning, and weapon types\n * Handles firing rates, projectile patterns, and visual effects\n */\nexport class WeaponSystem {\n    constructor(owner, gameObjectManager) {\n        this.owner = owner; // The ship that owns this weapon system\n        this.gameObjectManager = gameObjectManager;\n        \n        // Firing properties\n        this.fireRate = 300; // milliseconds between shots\n        this.lastFireTime = 0;\n        this.canFire = true;\n        \n        // Projectile properties\n        this.projectileSpeed = 600;\n        this.projectileDamage = 1;\n        this.projectileLifetime = 3000;\n        this.projectileType = 'player';\n        \n        // Weapon patterns\n        this.currentPattern = 'single'; // 'single', 'double', 'triple', 'spread'\n        this.spreadAngle = Math.PI / 6; // 30 degrees for spread pattern\n        \n        // Visual effects\n        this.muzzleFlashDuration = 100; // milliseconds\n        this.muzzleFlashTime = 0;\n        this.muzzleFlashPositions = [];\n        \n        // Audio properties (for future sound integration)\n        this.fireSound = null;\n        this.soundVolume = 0.5;\n        \n        // Initialize projectile pool\n        this.initializeProjectilePool();\n        \n        console.log('WeaponSystem initialized for owner:', owner.constructor.name);\n    }\n    \n    /**\n     * Initialize object pool for projectiles\n     */\n    initializeProjectilePool() {\n        // Create projectile pool if it doesn't exist\n        if (!this.gameObjectManager.pools.has('projectile')) {\n            this.gameObjectManager.createPool(\n                'projectile',\n                () => new Projectile(),\n                (projectile) => projectile.reset(),\n                20 // Initial pool size\n            );\n        }\n    }\n    \n    /**\n     * Update weapon system\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    update(deltaTime) {\n        // Update firing cooldown\n        if (!this.canFire) {\n            this.lastFireTime += deltaTime;\n            if (this.lastFireTime >= this.fireRate) {\n                this.canFire = true;\n                this.lastFireTime = 0;\n            }\n        }\n        \n        // Update muzzle flash effect\n        if (this.muzzleFlashTime > 0) {\n            this.muzzleFlashTime -= deltaTime;\n            if (this.muzzleFlashTime <= 0) {\n                this.muzzleFlashPositions = [];\n            }\n        }\n    }\n    \n    /**\n     * Attempt to fire weapon\n     * @param {Vector2} direction - Direction to fire (optional, defaults to up)\n     * @returns {boolean} True if weapon fired successfully\n     */\n    fire(direction = Vector2.up()) {\n        console.log('WeaponSystem.fire() called, canFire:', this.canFire);\n        if (!this.canFire) {\n            console.log('Cannot fire - weapon on cooldown');\n            return false;\n        }\n        \n        // Fire based on current pattern\n        switch (this.currentPattern) {\n            case 'single':\n                this.fireSingle(direction);\n                break;\n            case 'double':\n                this.fireDouble(direction);\n                break;\n            case 'triple':\n                this.fireTriple(direction);\n                break;\n            case 'spread':\n                this.fireSpread(direction);\n                break;\n            default:\n                this.fireSingle(direction);\n                break;\n        }\n        \n        // Set firing cooldown\n        this.canFire = false;\n        this.lastFireTime = 0;\n        \n        // Trigger muzzle flash effect\n        this.triggerMuzzleFlash();\n        \n        // Play fire sound (if available)\n        this.playFireSound();\n        \n        return true;\n    }\n    \n    /**\n     * Fire single projectile\n     * @param {Vector2} direction - Direction to fire\n     */\n    fireSingle(direction) {\n        const firePosition = this.getFirePosition();\n        this.createProjectile(firePosition, direction);\n    }\n    \n    /**\n     * Fire double projectiles (side by side)\n     * @param {Vector2} direction - Direction to fire\n     */\n    fireDouble(direction) {\n        const firePosition = this.getFirePosition();\n        const offset = direction.perpendicular().multiply(8); // 8 pixels apart\n        \n        // Left projectile\n        this.createProjectile(firePosition.subtract(offset), direction);\n        // Right projectile\n        this.createProjectile(firePosition.add(offset), direction);\n    }\n    \n    /**\n     * Fire triple projectiles (center + sides)\n     * @param {Vector2} direction - Direction to fire\n     */\n    fireTriple(direction) {\n        const firePosition = this.getFirePosition();\n        const offset = direction.perpendicular().multiply(12); // 12 pixels apart\n        \n        // Center projectile\n        this.createProjectile(firePosition, direction);\n        // Left projectile\n        this.createProjectile(firePosition.subtract(offset), direction);\n        // Right projectile\n        this.createProjectile(firePosition.add(offset), direction);\n    }\n    \n    /**\n     * Fire spread pattern projectiles\n     * @param {Vector2} direction - Base direction to fire\n     */\n    fireSpread(direction) {\n        const firePosition = this.getFirePosition();\n        const baseAngle = direction.angle();\n        const angleStep = this.spreadAngle / 2; // Spread across spreadAngle\n        \n        // Fire 5 projectiles in spread pattern\n        for (let i = -2; i <= 2; i++) {\n            const angle = baseAngle + (i * angleStep);\n            const spreadDirection = Vector2.fromAngle(angle);\n            this.createProjectile(firePosition, spreadDirection);\n        }\n    }\n    \n    /**\n     * Create and initialize a projectile\n     * @param {Vector2} position - Starting position\n     * @param {Vector2} direction - Direction to fire\n     */\n    createProjectile(position, direction) {\n        // Create projectile directly (bypass pool for now)\n        const projectile = new Projectile();\n        \n        // Initialize projectile\n        projectile.initialize(\n            position,\n            direction,\n            this.projectileSpeed,\n            this.projectileType,\n            this.owner\n        );\n        \n        // Set projectile properties\n        projectile.damage = this.projectileDamage;\n        projectile.lifetime = this.projectileLifetime;\n        \n        // Add to game object manager\n        this.gameObjectManager.add(projectile);\n        \n        console.log('Projectile created at:', projectile.position.toString(), 'with velocity:', projectile.velocity.toString());\n    }\n    \n    /**\n     * Get the position where projectiles should spawn\n     * @returns {Vector2} Fire position\n     */\n    getFirePosition() {\n        // Default to owner's position with slight forward offset\n        const ownerPos = this.owner.position.clone();\n        \n        // Offset based on owner's facing direction (assuming up for player)\n        const forwardOffset = new Vector2(0, -this.owner.height / 2 - 5);\n        \n        return ownerPos.add(forwardOffset);\n    }\n    \n    /**\n     * Trigger muzzle flash visual effect\n     */\n    triggerMuzzleFlash() {\n        this.muzzleFlashTime = this.muzzleFlashDuration;\n        \n        // Store muzzle flash positions based on current pattern\n        this.muzzleFlashPositions = [];\n        \n        switch (this.currentPattern) {\n            case 'single':\n                this.muzzleFlashPositions.push(this.getFirePosition());\n                break;\n            case 'double':\n                const firePos = this.getFirePosition();\n                const offset = new Vector2(8, 0);\n                this.muzzleFlashPositions.push(firePos.subtract(offset));\n                this.muzzleFlashPositions.push(firePos.add(offset));\n                break;\n            case 'triple':\n                const firePos3 = this.getFirePosition();\n                const offset3 = new Vector2(12, 0);\n                this.muzzleFlashPositions.push(firePos3);\n                this.muzzleFlashPositions.push(firePos3.subtract(offset3));\n                this.muzzleFlashPositions.push(firePos3.add(offset3));\n                break;\n            case 'spread':\n                // Single muzzle flash for spread pattern\n                this.muzzleFlashPositions.push(this.getFirePosition());\n                break;\n        }\n    }\n    \n    /**\n     * Play fire sound effect\n     */\n    playFireSound() {\n        // Placeholder for sound system integration\n        if (this.fireSound && typeof this.fireSound.play === 'function') {\n            this.fireSound.volume = this.soundVolume;\n            this.fireSound.currentTime = 0;\n            this.fireSound.play().catch(e => {\n                // Handle audio play errors silently\n                console.warn('Could not play fire sound:', e);\n            });\n        }\n    }\n    \n    /**\n     * Render weapon effects (muzzle flash, etc.)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    render(ctx) {\n        if (this.muzzleFlashTime > 0) {\n            this.renderMuzzleFlash(ctx);\n        }\n    }\n    \n    /**\n     * Render muzzle flash effect\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderMuzzleFlash(ctx) {\n        const flashAlpha = this.muzzleFlashTime / this.muzzleFlashDuration;\n        \n        ctx.save();\n        ctx.globalAlpha = flashAlpha;\n        \n        for (const flashPos of this.muzzleFlashPositions) {\n            // Create radial gradient for muzzle flash\n            const gradient = ctx.createRadialGradient(\n                flashPos.x, flashPos.y, 0,\n                flashPos.x, flashPos.y, 15\n            );\n            gradient.addColorStop(0, '#FFFFFF');\n            gradient.addColorStop(0.3, '#FFD700');\n            gradient.addColorStop(0.6, '#FF6B35');\n            gradient.addColorStop(1, 'rgba(255, 107, 53, 0)');\n            \n            ctx.fillStyle = gradient;\n            ctx.beginPath();\n            ctx.arc(flashPos.x, flashPos.y, 15, 0, Math.PI * 2);\n            ctx.fill();\n            \n            // Add bright center flash\n            ctx.fillStyle = '#FFFFFF';\n            ctx.beginPath();\n            ctx.arc(flashPos.x, flashPos.y, 3, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        \n        ctx.restore();\n    }\n    \n    /**\n     * Set weapon firing pattern\n     * @param {string} pattern - Pattern name ('single', 'double', 'triple', 'spread')\n     */\n    setPattern(pattern) {\n        const validPatterns = ['single', 'double', 'triple', 'spread'];\n        if (validPatterns.includes(pattern)) {\n            this.currentPattern = pattern;\n            console.log(`Weapon pattern changed to: ${pattern}`);\n        } else {\n            console.warn(`Invalid weapon pattern: ${pattern}`);\n        }\n    }\n    \n    /**\n     * Set firing rate\n     * @param {number} rateMs - Time between shots in milliseconds\n     */\n    setFireRate(rateMs) {\n        this.fireRate = Math.max(50, rateMs); // Minimum 50ms between shots\n    }\n    \n    /**\n     * Set projectile speed\n     * @param {number} speed - Projectile speed in pixels per second\n     */\n    setProjectileSpeed(speed) {\n        this.projectileSpeed = Math.max(100, speed); // Minimum speed\n    }\n    \n    /**\n     * Set projectile damage\n     * @param {number} damage - Damage per projectile\n     */\n    setProjectileDamage(damage) {\n        this.projectileDamage = Math.max(1, damage); // Minimum 1 damage\n    }\n    \n    /**\n     * Set spread angle for spread pattern\n     * @param {number} angleRadians - Spread angle in radians\n     */\n    setSpreadAngle(angleRadians) {\n        this.spreadAngle = Math.max(0, Math.min(Math.PI, angleRadians)); // 0 to 180 degrees\n    }\n    \n    /**\n     * Check if weapon can fire\n     * @returns {boolean} True if weapon is ready to fire\n     */\n    isReady() {\n        return this.canFire;\n    }\n    \n    /**\n     * Get firing cooldown progress (0-1)\n     * @returns {number} Cooldown progress\n     */\n    getCooldownProgress() {\n        if (this.canFire) return 1.0;\n        return this.lastFireTime / this.fireRate;\n    }\n    \n    /**\n     * Force weapon to be ready (for power-ups, etc.)\n     */\n    resetCooldown() {\n        this.canFire = true;\n        this.lastFireTime = 0;\n    }\n    \n    /**\n     * Get weapon statistics\n     * @returns {object} Weapon stats\n     */\n    getStats() {\n        return {\n            pattern: this.currentPattern,\n            fireRate: this.fireRate,\n            projectileSpeed: this.projectileSpeed,\n            projectileDamage: this.projectileDamage,\n            isReady: this.canFire,\n            cooldownProgress: this.getCooldownProgress()\n        };\n    }\n}", "import { GameObject } from '../utils/GameObject.js';\nimport { Vector2 } from '../utils/Vector2.js';\nimport { WeaponSystem } from '../systems/WeaponSystem.js';\n\n/**\n * PlayerShip class - Represents the player's ship with movement, rendering, and collision\n * Implements smooth movement with boundary checking and basic sprite rendering\n */\nexport class PlayerShip extends GameObject {\n    constructor(x, y, canvasWidth, canvasHeight, gameObjectManager = null) {\n        super(x, y);\n        \n        // Canvas boundaries for movement constraints\n        this.canvasWidth = canvasWidth;\n        this.canvasHeight = canvasHeight;\n        \n        // Ship properties\n        this.maxSpeed = 300; // pixels per second\n        this.acceleration = 800; // pixels per second squared\n        this.friction = 0.85; // velocity damping factor\n        \n        // Health and lives system\n        this.maxHealth = 100;\n        this.health = this.maxHealth;\n        this.maxLives = 3;\n        this.lives = this.maxLives;\n        this.isInvulnerable = false;\n        this.invulnerabilityDuration = 2000; // 2 seconds in milliseconds\n        this.invulnerabilityTimer = 0;\n        this.isDestroyed = false;\n        \n        // Damage feedback\n        this.damageFlashTimer = 0;\n        this.damageFlashDuration = 200; // 200ms flash duration\n        this.isFlashing = false;\n        \n        // Visual properties\n        this.width = 32;\n        this.height = 48;\n        this.collisionRadius = 16;\n        \n        // Ship boundaries (keep ship fully on screen)\n        this.boundaryPadding = Math.max(this.width, this.height) / 2;\n        \n        // Animation properties\n        this.animationTime = 0;\n        this.thrusterAnimationSpeed = 8; // cycles per second\n        \n        // Movement state\n        this.isMoving = false;\n        this.movementInput = new Vector2(0, 0);\n        \n        // Weapon system\n        this.weaponSystem = null;\n        if (gameObjectManager) {\n            this.weaponSystem = new WeaponSystem(this, gameObjectManager);\n        }\n        \n        // Add player tag\n        this.addTag('player');\n        \n        console.log('PlayerShip created at position:', this.position.toString());\n    }\n    \n    /**\n     * Update ship movement and animation\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} movementInput - Normalized movement input vector\n     */\n    update(deltaTime, movementInput = new Vector2(0, 0)) {\n        if (!this.active) return;\n        \n        // Store movement input for animation\n        this.movementInput = movementInput.clone();\n        this.isMoving = movementInput.magnitude() > 0.1;\n        \n        // Apply movement with acceleration\n        if (this.isMoving) {\n            // Calculate target velocity\n            const targetVelocity = movementInput.multiply(this.maxSpeed);\n            \n            // Apply acceleration towards target velocity\n            const velocityDiff = targetVelocity.subtract(this.velocity);\n            const accelerationForce = velocityDiff.multiply(this.acceleration * deltaTime / 1000);\n            \n            this.velocity.addInPlace(accelerationForce);\n            \n            // Clamp velocity to max speed\n            if (this.velocity.magnitude() > this.maxSpeed) {\n                this.velocity = this.velocity.normalize().multiply(this.maxSpeed);\n            }\n        } else {\n            // Apply friction when not moving\n            this.velocity.multiplyInPlace(Math.pow(this.friction, deltaTime / 16.67));\n            \n            // Stop very small velocities to prevent jitter\n            if (this.velocity.magnitude() < 1) {\n                this.velocity.set(0, 0);\n            }\n        }\n        \n        // Update position\n        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));\n        \n        // Apply boundary checking\n        this.checkBoundaries();\n        \n        // Update animation timer\n        this.animationTime += deltaTime / 1000;\n        \n        // Update health and lives timers\n        this.updateHealthSystem(deltaTime);\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n        \n        // Update weapon system\n        if (this.weaponSystem) {\n            this.weaponSystem.update(deltaTime);\n        }\n    }\n    \n    /**\n     * Check and enforce canvas boundaries\n     * Keeps the ship fully visible on screen\n     */\n    checkBoundaries() {\n        const leftBound = this.boundaryPadding;\n        const rightBound = this.canvasWidth - this.boundaryPadding;\n        const topBound = this.boundaryPadding;\n        const bottomBound = this.canvasHeight - this.boundaryPadding;\n        \n        let hitBoundary = false;\n        \n        // Check horizontal boundaries\n        if (this.position.x < leftBound) {\n            this.position.x = leftBound;\n            this.velocity.x = Math.max(0, this.velocity.x); // Stop leftward velocity\n            hitBoundary = true;\n        } else if (this.position.x > rightBound) {\n            this.position.x = rightBound;\n            this.velocity.x = Math.min(0, this.velocity.x); // Stop rightward velocity\n            hitBoundary = true;\n        }\n        \n        // Check vertical boundaries\n        if (this.position.y < topBound) {\n            this.position.y = topBound;\n            this.velocity.y = Math.max(0, this.velocity.y); // Stop upward velocity\n            hitBoundary = true;\n        } else if (this.position.y > bottomBound) {\n            this.position.y = bottomBound;\n            this.velocity.y = Math.min(0, this.velocity.y); // Stop downward velocity\n            hitBoundary = true;\n        }\n        \n        // Optional: Add screen shake or sound effect when hitting boundary\n        if (hitBoundary) {\n            // Could trigger boundary hit effect here\n        }\n    }\n    \n    /**\n     * Render the player ship with sprite graphics and animations\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor for smooth rendering\n     */\n    render(ctx, interpolation = 0) {\n        if (!this.visible) return;\n        \n        // Calculate interpolated position for smooth rendering\n        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));\n        \n        ctx.save();\n        ctx.translate(renderPos.x, renderPos.y);\n        ctx.rotate(this.rotation);\n        \n        // Draw ship body\n        this.drawShipBody(ctx);\n        \n        // Draw thruster effects when moving\n        if (this.isMoving) {\n            this.drawThrusterEffects(ctx);\n        }\n        \n        // Draw collision bounds in debug mode (optional)\n        if (window.DEBUG_MODE) {\n            this.drawDebugInfo(ctx);\n        }\n        \n        ctx.restore();\n        \n        // Render weapon effects (muzzle flash, etc.)\n        if (this.weaponSystem) {\n            this.weaponSystem.render(ctx);\n        }\n    }\n    \n    /**\n     * Draw the main ship body\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawShipBody(ctx) {\n        // Apply visual effects based on ship state\n        let shipAlpha = 1.0;\n        let shipColor = '#4A90E2';\n        let strokeColor = '#2E5C8A';\n        \n        // Invulnerability effect - flashing\n        if (this.isInvulnerable) {\n            const flashSpeed = 8; // flashes per second\n            const flashPhase = Math.sin(this.invulnerabilityTimer * flashSpeed * Math.PI / 1000);\n            shipAlpha = 0.3 + 0.7 * Math.abs(flashPhase);\n        }\n        \n        // Damage flash effect - red tint\n        if (this.isFlashing) {\n            const flashIntensity = this.damageFlashTimer / this.damageFlashDuration;\n            shipColor = this.interpolateColor('#4A90E2', '#FF4444', flashIntensity);\n            strokeColor = this.interpolateColor('#2E5C8A', '#CC2222', flashIntensity);\n        }\n        \n        // Apply alpha for transparency effects\n        ctx.globalAlpha = shipAlpha;\n        \n        // Ship body - triangular design\n        ctx.fillStyle = shipColor;\n        ctx.strokeStyle = strokeColor;\n        ctx.lineWidth = 2;\n        \n        ctx.beginPath();\n        // Main body (triangle pointing up)\n        ctx.moveTo(0, -this.height / 2); // Top point\n        ctx.lineTo(-this.width / 3, this.height / 3); // Bottom left\n        ctx.lineTo(this.width / 3, this.height / 3); // Bottom right\n        ctx.closePath();\n        ctx.fill();\n        ctx.stroke();\n        \n        // Ship details - cockpit\n        ctx.fillStyle = '#7BB3F0';\n        ctx.beginPath();\n        ctx.ellipse(0, -this.height / 4, this.width / 6, this.height / 8, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Wing details\n        ctx.fillStyle = '#2E5C8A';\n        ctx.fillRect(-this.width / 4, this.height / 6, this.width / 8, this.height / 4);\n        ctx.fillRect(this.width / 8, this.height / 6, this.width / 8, this.height / 4);\n        \n        // Engine glow (subtle)\n        ctx.fillStyle = '#87CEEB';\n        ctx.beginPath();\n        ctx.ellipse(-this.width / 6, this.height / 3, 3, 6, 0, 0, Math.PI * 2);\n        ctx.ellipse(this.width / 6, this.height / 3, 3, 6, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Restore global alpha\n        ctx.globalAlpha = 1.0;\n    }\n    \n    /**\n     * Draw thruster effects when ship is moving\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawThrusterEffects(ctx) {\n        // Animated thruster flames\n        const thrusterIntensity = this.velocity.magnitude() / this.maxSpeed;\n        const animationPhase = Math.sin(this.animationTime * this.thrusterAnimationSpeed * Math.PI * 2);\n        \n        // Base flame length based on movement intensity\n        const baseFlameLength = 15 * thrusterIntensity;\n        const flameVariation = 5 * animationPhase * thrusterIntensity;\n        const flameLength = baseFlameLength + flameVariation;\n        \n        if (flameLength > 2) {\n            // Left thruster\n            this.drawThrusterFlame(ctx, -this.width / 6, this.height / 3, flameLength);\n            \n            // Right thruster\n            this.drawThrusterFlame(ctx, this.width / 6, this.height / 3, flameLength);\n        }\n        \n        // Additional directional thrusters based on movement\n        this.drawDirectionalThrusters(ctx, thrusterIntensity, animationPhase);\n    }\n    \n    /**\n     * Draw individual thruster flame\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} x - X position relative to ship center\n     * @param {number} y - Y position relative to ship center\n     * @param {number} length - Flame length\n     */\n    drawThrusterFlame(ctx, x, y, length) {\n        const gradient = ctx.createLinearGradient(x, y, x, y + length);\n        gradient.addColorStop(0, '#FFD700'); // Gold at base\n        gradient.addColorStop(0.5, '#FF6B35'); // Orange in middle\n        gradient.addColorStop(1, 'rgba(255, 0, 0, 0)'); // Transparent red at tip\n        \n        ctx.fillStyle = gradient;\n        ctx.beginPath();\n        ctx.moveTo(x - 3, y);\n        ctx.lineTo(x + 3, y);\n        ctx.lineTo(x + 1, y + length);\n        ctx.lineTo(x - 1, y + length);\n        ctx.closePath();\n        ctx.fill();\n    }\n    \n    /**\n     * Draw directional thrusters based on movement direction\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} intensity - Movement intensity (0-1)\n     * @param {number} animationPhase - Animation phase (-1 to 1)\n     */\n    drawDirectionalThrusters(ctx, intensity, animationPhase) {\n        const thrusterSize = 3 * intensity;\n        const alpha = 0.7 * intensity;\n        \n        // Side thrusters for horizontal movement\n        if (Math.abs(this.movementInput.x) > 0.1) {\n            ctx.fillStyle = `rgba(135, 206, 235, ${alpha})`;\n            \n            if (this.movementInput.x > 0) {\n                // Moving right, show left thruster\n                ctx.fillRect(-this.width / 2 - thrusterSize, -2, thrusterSize, 4);\n            } else {\n                // Moving left, show right thruster\n                ctx.fillRect(this.width / 2, -2, thrusterSize, 4);\n            }\n        }\n        \n        // Forward thrusters for upward movement\n        if (this.movementInput.y < -0.1) {\n            ctx.fillStyle = `rgba(255, 215, 0, ${alpha})`;\n            const forwardThrusterLength = 8 * intensity * (1 + 0.3 * animationPhase);\n            ctx.fillRect(-2, -this.height / 2 - forwardThrusterLength, 4, forwardThrusterLength);\n        }\n    }\n    \n    /**\n     * Draw debug information (collision bounds, velocity, etc.)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawDebugInfo(ctx) {\n        // Collision circle\n        ctx.strokeStyle = '#FF0000';\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);\n        ctx.stroke();\n        \n        // Velocity vector\n        if (this.velocity.magnitude() > 1) {\n            ctx.strokeStyle = '#00FF00';\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            ctx.moveTo(0, 0);\n            const velocityScale = 0.1; // Scale down for visibility\n            ctx.lineTo(this.velocity.x * velocityScale, this.velocity.y * velocityScale);\n            ctx.stroke();\n        }\n        \n        // Ship center point\n        ctx.fillStyle = '#FFFF00';\n        ctx.fillRect(-1, -1, 2, 2);\n    }\n    \n    /**\n     * Get the ship's current speed\n     * @returns {number} Current speed in pixels per second\n     */\n    getCurrentSpeed() {\n        return this.velocity.magnitude();\n    }\n    \n    /**\n     * Check if ship is at the boundary\n     * @returns {object} Object indicating which boundaries are being touched\n     */\n    getBoundaryStatus() {\n        const leftBound = this.boundaryPadding;\n        const rightBound = this.canvasWidth - this.boundaryPadding;\n        const topBound = this.boundaryPadding;\n        const bottomBound = this.canvasHeight - this.boundaryPadding;\n        \n        return {\n            left: this.position.x <= leftBound,\n            right: this.position.x >= rightBound,\n            top: this.position.y <= topBound,\n            bottom: this.position.y >= bottomBound\n        };\n    }\n    \n    /**\n     * Reset ship to starting position and state\n     * @param {number} x - Starting X position\n     * @param {number} y - Starting Y position\n     */\n    resetToPosition(x, y) {\n        this.position.set(x, y);\n        this.velocity.set(0, 0);\n        this.rotation = 0;\n        this.animationTime = 0;\n        this.isMoving = false;\n        this.movementInput.set(0, 0);\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        \n        // Don't reset health/lives here - that's handled by respawn() or resetHealthAndLives()\n    }\n    \n    /**\n     * Update canvas dimensions (for window resize)\n     * @param {number} width - New canvas width\n     * @param {number} height - New canvas height\n     */\n    updateCanvasDimensions(width, height) {\n        this.canvasWidth = width;\n        this.canvasHeight = height;\n        \n        // Re-check boundaries with new dimensions\n        this.checkBoundaries();\n    }\n    \n    /**\n     * Fire weapon in specified direction\n     * @param {Vector2} direction - Direction to fire (optional, defaults to up)\n     * @returns {boolean} True if weapon fired successfully\n     */\n    fire(direction = Vector2.up()) {\n        console.log('PlayerShip.fire() called, weaponSystem exists:', !!this.weaponSystem);\n        if (this.weaponSystem) {\n            const result = this.weaponSystem.fire(direction);\n            console.log('WeaponSystem.fire() returned:', result);\n            return result;\n        }\n        return false;\n    }\n    \n    /**\n     * Set weapon system (for initialization after construction)\n     * @param {WeaponSystem} weaponSystem - Weapon system instance\n     */\n    setWeaponSystem(weaponSystem) {\n        this.weaponSystem = weaponSystem;\n    }\n    \n    /**\n     * Get weapon system\n     * @returns {WeaponSystem|null} Current weapon system\n     */\n    getWeaponSystem() {\n        return this.weaponSystem;\n    }\n    \n    /**\n     * Check if weapon is ready to fire\n     * @returns {boolean} True if weapon can fire\n     */\n    canFire() {\n        return this.weaponSystem ? this.weaponSystem.isReady() : false;\n    }\n    \n    /**\n     * Set weapon pattern\n     * @param {string} pattern - Weapon pattern ('single', 'double', 'triple', 'spread')\n     */\n    setWeaponPattern(pattern) {\n        if (this.weaponSystem) {\n            this.weaponSystem.setPattern(pattern);\n        }\n    }\n    \n    /**\n     * Get weapon statistics\n     * @returns {object|null} Weapon stats or null if no weapon system\n     */\n    getWeaponStats() {\n        return this.weaponSystem ? this.weaponSystem.getStats() : null;\n    }\n    \n    /**\n     * Update health system timers (invulnerability and damage flash)\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    updateHealthSystem(deltaTime) {\n        // Update invulnerability timer\n        if (this.isInvulnerable) {\n            this.invulnerabilityTimer -= deltaTime;\n            if (this.invulnerabilityTimer <= 0) {\n                this.isInvulnerable = false;\n                this.invulnerabilityTimer = 0;\n            }\n        }\n        \n        // Update damage flash timer\n        if (this.isFlashing) {\n            this.damageFlashTimer -= deltaTime;\n            if (this.damageFlashTimer <= 0) {\n                this.isFlashing = false;\n                this.damageFlashTimer = 0;\n            }\n        }\n    }\n    \n    /**\n     * Take damage and handle health/lives logic\n     * @param {number} damage - Amount of damage to take\n     * @returns {object} Result object with damage taken, health remaining, lives remaining, and destroyed status\n     */\n    takeDamage(damage) {\n        // Can't take damage if invulnerable or already destroyed\n        if (this.isInvulnerable || this.isDestroyed) {\n            return {\n                damageTaken: 0,\n                health: this.health,\n                lives: this.lives,\n                destroyed: this.isDestroyed\n            };\n        }\n        \n        // Apply damage\n        const actualDamage = Math.min(damage, this.health);\n        this.health -= actualDamage;\n        \n        // Trigger damage flash effect\n        this.isFlashing = true;\n        this.damageFlashTimer = this.damageFlashDuration;\n        \n        console.log(`PlayerShip took ${actualDamage} damage. Health: ${this.health}/${this.maxHealth}, Lives: ${this.lives}`);\n        \n        // Check if ship is destroyed\n        if (this.health <= 0) {\n            this.destroyShip();\n        } else {\n            // Grant brief invulnerability after taking damage\n            this.isInvulnerable = true;\n            this.invulnerabilityTimer = this.invulnerabilityDuration;\n        }\n        \n        return {\n            damageTaken: actualDamage,\n            health: this.health,\n            lives: this.lives,\n            destroyed: this.isDestroyed\n        };\n    }\n    \n    /**\n     * Destroy the ship and handle respawn logic\n     */\n    destroyShip() {\n        this.health = 0;\n        this.lives--;\n        \n        console.log(`PlayerShip destroyed! Lives remaining: ${this.lives}`);\n        \n        if (this.lives <= 0) {\n            // Game over - no more lives\n            this.isDestroyed = true;\n            this.active = false;\n            console.log('Game Over - No lives remaining');\n        } else {\n            // Respawn with full health and invulnerability\n            this.respawn();\n        }\n    }\n    \n    /**\n     * Respawn the ship with full health and temporary invulnerability\n     */\n    respawn() {\n        // Reset health\n        this.health = this.maxHealth;\n        \n        // Reset position to starting location\n        const startX = this.canvasWidth / 2;\n        const startY = this.canvasHeight - 100;\n        this.resetToPosition(startX, startY);\n        \n        // Grant extended invulnerability after respawn\n        this.isInvulnerable = true;\n        this.invulnerabilityTimer = this.invulnerabilityDuration * 2; // Double duration after respawn\n        \n        // Reset damage flash\n        this.isFlashing = false;\n        this.damageFlashTimer = 0;\n        \n        console.log(`PlayerShip respawned with full health. Lives: ${this.lives}`);\n    }\n    \n    /**\n     * Heal the ship by a specified amount\n     * @param {number} healAmount - Amount of health to restore\n     * @returns {number} Actual amount healed\n     */\n    heal(healAmount) {\n        if (this.isDestroyed) return 0;\n        \n        const actualHeal = Math.min(healAmount, this.maxHealth - this.health);\n        this.health += actualHeal;\n        \n        console.log(`PlayerShip healed for ${actualHeal}. Health: ${this.health}/${this.maxHealth}`);\n        return actualHeal;\n    }\n    \n    /**\n     * Add extra lives\n     * @param {number} extraLives - Number of lives to add\n     */\n    addLives(extraLives) {\n        this.lives += extraLives;\n        console.log(`PlayerShip gained ${extraLives} lives. Total lives: ${this.lives}`);\n    }\n    \n    /**\n     * Get current health status\n     * @returns {object} Health status object\n     */\n    getHealthStatus() {\n        return {\n            health: this.health,\n            maxHealth: this.maxHealth,\n            healthPercentage: this.health / this.maxHealth,\n            lives: this.lives,\n            maxLives: this.maxLives,\n            isInvulnerable: this.isInvulnerable,\n            invulnerabilityTimeRemaining: this.invulnerabilityTimer,\n            isDestroyed: this.isDestroyed,\n            isFlashing: this.isFlashing\n        };\n    }\n    \n    /**\n     * Reset health and lives to maximum (for new game)\n     */\n    resetHealthAndLives() {\n        this.health = this.maxHealth;\n        this.lives = this.maxLives;\n        this.isInvulnerable = false;\n        this.invulnerabilityTimer = 0;\n        this.isDestroyed = false;\n        this.isFlashing = false;\n        this.damageFlashTimer = 0;\n        \n        console.log('PlayerShip health and lives reset to maximum');\n    }\n    \n    /**\n     * Interpolate between two hex colors\n     * @param {string} color1 - Starting color (hex format)\n     * @param {string} color2 - Ending color (hex format)\n     * @param {number} factor - Interpolation factor (0-1)\n     * @returns {string} Interpolated color in hex format\n     */\n    interpolateColor(color1, color2, factor) {\n        // Clamp factor between 0 and 1\n        factor = Math.max(0, Math.min(1, factor));\n        \n        // Parse hex colors\n        const hex1 = color1.replace('#', '');\n        const hex2 = color2.replace('#', '');\n        \n        const r1 = parseInt(hex1.substr(0, 2), 16);\n        const g1 = parseInt(hex1.substr(2, 2), 16);\n        const b1 = parseInt(hex1.substr(4, 2), 16);\n        \n        const r2 = parseInt(hex2.substr(0, 2), 16);\n        const g2 = parseInt(hex2.substr(2, 2), 16);\n        const b2 = parseInt(hex2.substr(4, 2), 16);\n        \n        // Interpolate each channel\n        const r = Math.round(r1 + (r2 - r1) * factor);\n        const g = Math.round(g1 + (g2 - g1) * factor);\n        const b = Math.round(b1 + (b2 - b1) * factor);\n        \n        // Convert back to hex\n        const toHex = (n) => {\n            const hex = n.toString(16);\n            return hex.length === 1 ? '0' + hex : hex;\n        };\n        \n        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;\n    }\n}", "/**\n * Object Pool for efficient memory management\n * Reuses objects to avoid frequent allocation/deallocation\n */\nexport class ObjectPool {\n    constructor(createFn, resetFn, initialSize = 10) {\n        this.createFn = createFn;\n        this.resetFn = resetFn;\n        this.pool = [];\n        this.active = [];\n        \n        // Pre-populate pool\n        for (let i = 0; i < initialSize; i++) {\n            this.pool.push(this.createFn());\n        }\n    }\n    \n    // Get an object from the pool\n    get() {\n        let obj;\n        if (this.pool.length > 0) {\n            obj = this.pool.pop();\n        } else {\n            obj = this.createFn();\n        }\n        \n        this.active.push(obj);\n        return obj;\n    }\n    \n    // Return an object to the pool\n    release(obj) {\n        const index = this.active.indexOf(obj);\n        if (index !== -1) {\n            this.active.splice(index, 1);\n            this.resetFn(obj);\n            this.pool.push(obj);\n        }\n    }\n    \n    // Release all active objects\n    releaseAll() {\n        while (this.active.length > 0) {\n            const obj = this.active.pop();\n            this.resetFn(obj);\n            this.pool.push(obj);\n        }\n    }\n    \n    // Get pool statistics\n    getStats() {\n        return {\n            pooled: this.pool.length,\n            active: this.active.length,\n            total: this.pool.length + this.active.length\n        };\n    }\n}", "import { ObjectPool } from './ObjectPool.js';\n\n/**\n * GameObjectManager - Manages collections of game objects\n * Provides efficient update, render, and collision detection\n */\nexport class GameObjectManager {\n    constructor() {\n        this.objects = [];\n        this.objectsToAdd = [];\n        this.objectsToRemove = [];\n        this.pools = new Map();\n    }\n    \n    // Add object to manager\n    add(object) {\n        this.objectsToAdd.push(object);\n    }\n    \n    // Remove object from manager\n    remove(object) {\n        this.objectsToRemove.push(object);\n    }\n    \n    // Create object pool for a specific type\n    createPool(type, createFn, resetFn, initialSize = 10) {\n        this.pools.set(type, new ObjectPool(createFn, resetFn, initialSize));\n    }\n    \n    // Get object from pool\n    getFromPool(type) {\n        const pool = this.pools.get(type);\n        if (pool) {\n            return pool.get();\n        }\n        throw new Error(`Pool for type '${type}' not found`);\n    }\n    \n    // Return object to pool\n    returnToPool(type, object) {\n        const pool = this.pools.get(type);\n        if (pool) {\n            pool.release(object);\n            this.remove(object);\n        }\n    }\n    \n    // Update all objects\n    update(deltaTime) {\n        // Process additions and removals\n        this.processAdditions();\n        this.processRemovals();\n        \n        // Update all active objects\n        for (let i = this.objects.length - 1; i >= 0; i--) {\n            const object = this.objects[i];\n            \n            if (object.destroyed) {\n                this.objectsToRemove.push(object);\n                continue;\n            }\n            \n            if (object.active) {\n                object.update(deltaTime);\n            }\n        }\n    }\n    \n    // Render all objects\n    render(ctx, interpolation = 0) {\n        for (const object of this.objects) {\n            if (object.visible && !object.destroyed) {\n                object.render(ctx, interpolation);\n            }\n        }\n    }\n    \n    // Process pending additions\n    processAdditions() {\n        if (this.objectsToAdd.length > 0) {\n            this.objects.push(...this.objectsToAdd);\n            this.objectsToAdd.length = 0;\n        }\n    }\n    \n    // Process pending removals\n    processRemovals() {\n        if (this.objectsToRemove.length > 0) {\n            for (const objectToRemove of this.objectsToRemove) {\n                const index = this.objects.indexOf(objectToRemove);\n                if (index !== -1) {\n                    this.objects.splice(index, 1);\n                }\n            }\n            this.objectsToRemove.length = 0;\n        }\n    }\n    \n    // Find objects by tag\n    findByTag(tag) {\n        return this.objects.filter(obj => obj.hasTag(tag));\n    }\n    \n    // Find object by ID\n    findById(id) {\n        return this.objects.find(obj => obj.id === id);\n    }\n    \n    // Get all active objects\n    getActive() {\n        return this.objects.filter(obj => obj.active && !obj.destroyed);\n    }\n    \n    // Get all visible objects\n    getVisible() {\n        return this.objects.filter(obj => obj.visible && !obj.destroyed);\n    }\n    \n    // Collision detection between groups\n    checkCollisions(group1Tag, group2Tag, callback) {\n        const group1 = this.findByTag(group1Tag);\n        const group2 = this.findByTag(group2Tag);\n        \n        for (const obj1 of group1) {\n            if (!obj1.active) continue;\n            \n            for (const obj2 of group2) {\n                if (!obj2.active) continue;\n                \n                if (obj1.collidesWith(obj2)) {\n                    callback(obj1, obj2);\n                }\n            }\n        }\n    }\n    \n    // Broad phase collision detection using spatial partitioning\n    checkCollisionsOptimized(group1Tag, group2Tag, callback, cellSize = 64) {\n        const group1 = this.findByTag(group1Tag);\n        const group2 = this.findByTag(group2Tag);\n        \n        // Simple spatial hash for broad phase\n        const spatialHash = new Map();\n        \n        // Hash group2 objects\n        for (const obj of group2) {\n            if (!obj.active) continue;\n            \n            const cellX = Math.floor(obj.position.x / cellSize);\n            const cellY = Math.floor(obj.position.y / cellSize);\n            const key = `${cellX},${cellY}`;\n            \n            if (!spatialHash.has(key)) {\n                spatialHash.set(key, []);\n            }\n            spatialHash.get(key).push(obj);\n        }\n        \n        // Check group1 objects against nearby cells\n        for (const obj1 of group1) {\n            if (!obj1.active) continue;\n            \n            const cellX = Math.floor(obj1.position.x / cellSize);\n            const cellY = Math.floor(obj1.position.y / cellSize);\n            \n            // Check surrounding cells\n            for (let dx = -1; dx <= 1; dx++) {\n                for (let dy = -1; dy <= 1; dy++) {\n                    const key = `${cellX + dx},${cellY + dy}`;\n                    const nearbyObjects = spatialHash.get(key);\n                    \n                    if (nearbyObjects) {\n                        for (const obj2 of nearbyObjects) {\n                            if (obj1.collidesWith(obj2)) {\n                                callback(obj1, obj2);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n    \n    // Clear all objects\n    clear() {\n        this.objects.length = 0;\n        this.objectsToAdd.length = 0;\n        this.objectsToRemove.length = 0;\n        \n        // Clear all pools\n        for (const pool of this.pools.values()) {\n            pool.releaseAll();\n        }\n    }\n    \n    // Get statistics\n    getStats() {\n        const poolStats = {};\n        for (const [type, pool] of this.pools.entries()) {\n            poolStats[type] = pool.getStats();\n        }\n        \n        return {\n            totalObjects: this.objects.length,\n            activeObjects: this.getActive().length,\n            visibleObjects: this.getVisible().length,\n            pendingAdditions: this.objectsToAdd.length,\n            pendingRemovals: this.objectsToRemove.length,\n            pools: poolStats\n        };\n    }\n}", "import { InputManager } from '../input/InputManager.js';\nimport { PlayerShip } from '../entities/PlayerShip.js';\nimport { GameObjectManager } from '../utils/GameObjectManager.js';\n\n/**\n * Core Game Engine - Main game loop and state management\n * Implements fixed timestep game loop with 60 FPS target\n */\nexport class GameEngine {\n    constructor(canvas, uiElement) {\n        this.canvas = canvas;\n        this.ctx = canvas.getContext('2d');\n        this.uiElement = uiElement;\n        \n        // Game state\n        this.isRunning = false;\n        this.isPaused = false;\n        \n        // Fixed timestep configuration\n        this.targetFPS = 60;\n        this.fixedTimeStep = 1000 / this.targetFPS; // 16.67ms per frame\n        this.maxFrameTime = 250; // Maximum frame time to prevent spiral of death\n        \n        // Timing variables\n        this.lastFrameTime = 0;\n        this.accumulator = 0;\n        this.currentTime = 0;\n        \n        // Performance tracking\n        this.frameCount = 0;\n        this.fpsTimer = 0;\n        this.currentFPS = 0;\n        \n        // Bind methods\n        this.gameLoop = this.gameLoop.bind(this);\n    }\n    \n    async init() {\n        console.log('Initializing WarpSpace Game Engine...');\n        \n        // Set up canvas properties\n        this.setupCanvas();\n        \n        // Initialize game systems (placeholder for now)\n        this.initializeSystems();\n        \n        // Start the game loop\n        this.start();\n        \n        return Promise.resolve();\n    }\n    \n    setupCanvas() {\n        // Set up canvas for crisp pixel rendering\n        this.ctx.imageSmoothingEnabled = false;\n        \n        // Set canvas size\n        this.canvas.width = 800;\n        this.canvas.height = 600;\n        \n        console.log(`Canvas initialized: ${this.canvas.width}x${this.canvas.height}`);\n    }\n    \n    initializeSystems() {\n        // Initialize input manager\n        this.inputManager = new InputManager(this.canvas);\n        \n        // Initialize game object manager\n        this.gameObjectManager = new GameObjectManager();\n        \n        // Initialize player ship at bottom center of screen\n        const startX = this.canvas.width / 2;\n        const startY = this.canvas.height - 100; // 100 pixels from bottom\n        this.playerShip = new PlayerShip(startX, startY, this.canvas.width, this.canvas.height, this.gameObjectManager);\n        \n        // Don't add player ship to game object manager - we'll handle it separately\n        \n        console.log('Game systems initialized');\n    }\n    \n    start() {\n        if (!this.isRunning) {\n            this.isRunning = true;\n            this.isPaused = false;\n            this.currentTime = performance.now();\n            this.lastFrameTime = this.currentTime;\n            this.accumulator = 0;\n            this.frameCount = 0;\n            this.fpsTimer = 0;\n            requestAnimationFrame(this.gameLoop);\n            console.log('Game loop started with fixed timestep');\n        }\n    }\n    \n    pause() {\n        this.isPaused = true;\n        console.log('Game paused');\n    }\n    \n    resume() {\n        if (this.isPaused) {\n            this.isPaused = false;\n            // Reset timing to prevent large delta time jump\n            this.currentTime = performance.now();\n            this.lastFrameTime = this.currentTime;\n            this.accumulator = 0;\n            console.log('Game resumed');\n        }\n    }\n    \n    destroy() {\n        this.isRunning = false;\n        \n        // Cleanup input manager\n        if (this.inputManager) {\n            this.inputManager.destroy();\n        }\n        \n        console.log('Game engine destroyed');\n    }\n    \n    gameLoop(currentTime) {\n        if (!this.isRunning) return;\n        \n        // Calculate frame time and clamp to prevent spiral of death\n        let frameTime = currentTime - this.lastFrameTime;\n        if (frameTime > this.maxFrameTime) {\n            frameTime = this.maxFrameTime;\n        }\n        \n        this.lastFrameTime = currentTime;\n        \n        if (!this.isPaused) {\n            // Add frame time to accumulator\n            this.accumulator += frameTime;\n            \n            // Fixed timestep updates - run as many updates as needed\n            while (this.accumulator >= this.fixedTimeStep) {\n                this.update(this.fixedTimeStep);\n                this.accumulator -= this.fixedTimeStep;\n            }\n            \n            // Calculate interpolation factor for smooth rendering\n            const interpolation = this.accumulator / this.fixedTimeStep;\n            \n            // Always render (variable timestep for smooth visuals)\n            this.render(interpolation);\n            \n            // Update FPS counter\n            this.updateFPSCounter(frameTime);\n        }\n        \n        requestAnimationFrame(this.gameLoop);\n    }\n    \n    update(deltaTime) {\n        // Update input manager\n        if (this.inputManager) {\n            this.inputManager.update();\n        }\n        \n        // Update player ship with movement input (handle separately from GameObjectManager)\n        if (this.playerShip && this.inputManager) {\n            const movementInput = this.inputManager.getMovementVector();\n            this.playerShip.update(deltaTime, movementInput);\n            \n            // Handle weapon firing input\n            if (this.inputManager.isActionDown('fire')) {\n                this.playerShip.fire();\n            }\n            \n            // Debug: Test damage system (D key)\n            if (this.inputManager.isKeyPressed('KeyD')) {\n                this.playerShip.takeDamage(25); // Take 25 damage for testing\n            }\n            \n            // Debug: Test healing system (H key)\n            if (this.inputManager.isKeyPressed('KeyH')) {\n                this.playerShip.heal(25); // Heal 25 health for testing\n            }\n            \n            // Debug: Add extra life (L key)\n            if (this.inputManager.isKeyPressed('KeyL')) {\n                this.playerShip.addLives(1); // Add 1 life for testing\n            }\n        }\n        \n        // Update all other game objects (projectiles, enemies, etc.)\n        if (this.gameObjectManager) {\n            this.gameObjectManager.update(deltaTime);\n        }\n        \n        // Clean up out-of-bounds projectiles\n        this.cleanupProjectiles();\n        \n        // Placeholder for other game state updates\n        // This will be expanded in future tasks\n        // deltaTime is in milliseconds and represents the fixed timestep\n    }\n    \n    /**\n     * Clean up projectiles that are out of bounds\n     */\n    cleanupProjectiles() {\n        if (!this.gameObjectManager) return;\n        \n        const bounds = {\n            left: -50,\n            right: this.canvas.width + 50,\n            top: -50,\n            bottom: this.canvas.height + 50\n        };\n        \n        const projectiles = this.gameObjectManager.findByTag('projectile');\n        for (const projectile of projectiles) {\n            if (projectile.isOutOfBounds(bounds)) {\n                this.gameObjectManager.returnToPool('projectile', projectile);\n            }\n        }\n    }\n    \n    render(interpolation = 0) {\n        // Clear canvas\n        this.ctx.fillStyle = '#000011';\n        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n        \n        // Render basic space background\n        this.renderStarField();\n        \n        // Render player ship separately\n        if (this.playerShip) {\n            this.playerShip.render(this.ctx, interpolation);\n        }\n        \n        // Render all other game objects (projectiles, enemies, etc.)\n        if (this.gameObjectManager) {\n            this.gameObjectManager.render(this.ctx, interpolation);\n        }\n        \n        // Game title (smaller and positioned at top)\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '20px Arial';\n        this.ctx.textAlign = 'center';\n        this.ctx.fillText('WarpSpace', this.canvas.width / 2, 30);\n        \n        // Display input debug info\n        if (this.inputManager) {\n            this.renderInputDebug();\n            \n            // Render input manager (virtual joystick, etc.)\n            this.inputManager.render(this.ctx);\n        }\n        \n        // Display FPS counter\n        this.ctx.font = '12px Arial';\n        this.ctx.textAlign = 'left';\n        this.ctx.fillText(`FPS: ${this.currentFPS}`, 10, 20);\n        \n        // Display health and lives UI\n        this.renderHealthAndLivesUI();\n    }\n    \n    renderInputDebug() {\n        if (!this.inputManager) return;\n        \n        // Display movement input\n        const movement = this.inputManager.getMovementVector();\n        this.ctx.font = '12px Arial';\n        this.ctx.textAlign = 'left';\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.fillText(`Movement: ${movement.x.toFixed(2)}, ${movement.y.toFixed(2)}`, 10, 40);\n        \n        // Display active actions\n        const actions = ['fire', 'pause', 'interact'];\n        let yOffset = 60;\n        \n        for (const action of actions) {\n            if (this.inputManager.isActionDown(action)) {\n                this.ctx.fillStyle = '#00ff00';\n                this.ctx.fillText(`${action.toUpperCase()}: ACTIVE`, 10, yOffset);\n            } else {\n                this.ctx.fillStyle = '#666666';\n                this.ctx.fillText(`${action}: inactive`, 10, yOffset);\n            }\n            yOffset += 15;\n        }\n        \n        // Display mouse position\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.fillText(`Mouse: ${this.inputManager.mousePosition.x.toFixed(0)}, ${this.inputManager.mousePosition.y.toFixed(0)}`, 10, yOffset + 10);\n        \n        // Display touch info for mobile\n        if (this.inputManager.isTouchDevice) {\n            this.ctx.fillText(`Touch Device: ${this.inputManager.touches.size} touches`, 10, yOffset + 25);\n        }\n        \n        // Display game object manager stats\n        if (this.gameObjectManager) {\n            const stats = this.gameObjectManager.getStats();\n            this.ctx.fillText(`Objects: ${stats.totalObjects} (${stats.activeObjects} active)`, 10, yOffset + 40);\n            \n            const projectiles = this.gameObjectManager.findByTag('projectile');\n            this.ctx.fillText(`Projectiles: ${projectiles.length}`, 10, yOffset + 55);\n        }\n    }\n    \n    updateFPSCounter(frameTime) {\n        this.frameCount++;\n        this.fpsTimer += frameTime;\n        \n        // Update FPS display every second\n        if (this.fpsTimer >= 1000) {\n            this.currentFPS = Math.round((this.frameCount * 1000) / this.fpsTimer);\n            this.frameCount = 0;\n            this.fpsTimer = 0;\n        }\n    }\n    \n    renderStarField() {\n        // Simple star field effect\n        this.ctx.fillStyle = '#ffffff';\n        for (let i = 0; i < 100; i++) {\n            const x = (i * 37) % this.canvas.width;\n            const y = (i * 73) % this.canvas.height;\n            const size = (i % 3) + 1;\n            this.ctx.fillRect(x, y, size, size);\n        }\n    }\n    \n    /**\n     * Render health bar and lives counter UI\n     */\n    renderHealthAndLivesUI() {\n        if (!this.playerShip) return;\n        \n        const healthStatus = this.playerShip.getHealthStatus();\n        \n        // Health bar position (top-right corner)\n        const healthBarX = this.canvas.width - 220;\n        const healthBarY = 15;\n        const healthBarWidth = 200;\n        const healthBarHeight = 20;\n        \n        // Health bar background\n        this.ctx.fillStyle = '#333333';\n        this.ctx.fillRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);\n        \n        // Health bar border\n        this.ctx.strokeStyle = '#666666';\n        this.ctx.lineWidth = 2;\n        this.ctx.strokeRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);\n        \n        // Health bar fill\n        const healthWidth = healthBarWidth * healthStatus.healthPercentage;\n        let healthColor = '#00ff00'; // Green for healthy\n        \n        if (healthStatus.healthPercentage < 0.3) {\n            healthColor = '#ff0000'; // Red for critical\n        } else if (healthStatus.healthPercentage < 0.6) {\n            healthColor = '#ffaa00'; // Orange for damaged\n        }\n        \n        this.ctx.fillStyle = healthColor;\n        this.ctx.fillRect(healthBarX + 2, healthBarY + 2, healthWidth - 4, healthBarHeight - 4);\n        \n        // Health text\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '12px Arial';\n        this.ctx.textAlign = 'center';\n        this.ctx.fillText(\n            `${healthStatus.health}/${healthStatus.maxHealth}`, \n            healthBarX + healthBarWidth / 2, \n            healthBarY + healthBarHeight / 2 + 4\n        );\n        \n        // Lives counter (below health bar)\n        const livesY = healthBarY + healthBarHeight + 25;\n        this.ctx.textAlign = 'right';\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '16px Arial';\n        this.ctx.fillText(`Lives: ${healthStatus.lives}`, this.canvas.width - 20, livesY);\n        \n        // Draw life icons\n        const lifeIconSize = 16;\n        const lifeIconSpacing = 20;\n        const lifeIconStartX = this.canvas.width - 20 - (healthStatus.lives * lifeIconSpacing);\n        \n        for (let i = 0; i < healthStatus.lives; i++) {\n            const iconX = lifeIconStartX + (i * lifeIconSpacing);\n            const iconY = livesY + 10;\n            \n            // Draw small ship icon for each life\n            this.ctx.fillStyle = '#4A90E2';\n            this.ctx.beginPath();\n            this.ctx.moveTo(iconX, iconY - lifeIconSize / 2);\n            this.ctx.lineTo(iconX - lifeIconSize / 3, iconY + lifeIconSize / 3);\n            this.ctx.lineTo(iconX + lifeIconSize / 3, iconY + lifeIconSize / 3);\n            this.ctx.closePath();\n            this.ctx.fill();\n        }\n        \n        // Show invulnerability status\n        if (healthStatus.isInvulnerable) {\n            this.ctx.fillStyle = '#ffff00';\n            this.ctx.font = '12px Arial';\n            this.ctx.textAlign = 'right';\n            const invulnTime = (healthStatus.invulnerabilityTimeRemaining / 1000).toFixed(1);\n            this.ctx.fillText(`Invulnerable: ${invulnTime}s`, this.canvas.width - 20, livesY + 50);\n        }\n        \n        // Show game over message if destroyed\n        if (healthStatus.isDestroyed) {\n            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';\n            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n            \n            this.ctx.fillStyle = '#ff0000';\n            this.ctx.font = '48px Arial';\n            this.ctx.textAlign = 'center';\n            this.ctx.fillText('GAME OVER', this.canvas.width / 2, this.canvas.height / 2);\n            \n            this.ctx.fillStyle = '#ffffff';\n            this.ctx.font = '24px Arial';\n            this.ctx.fillText('No lives remaining', this.canvas.width / 2, this.canvas.height / 2 + 50);\n        }\n    }\n    \n    handleResize() {\n        // Handle window resize if needed\n        console.log('Window resized');\n    }\n}", "import { GameEngine } from './engine/GameEngine.js';\n\n// Initialize the game when DOM is loaded\ndocument.addEventListener('DOMContentLoaded', () => {\n    const canvas = document.getElementById('gameCanvas');\n    const ui = document.getElementById('ui');\n    \n    if (!canvas) {\n        console.error('Game canvas not found');\n        return;\n    }\n    \n    // Initialize game engine\n    const game = new GameEngine(canvas, ui);\n    \n    // Start the game\n    game.init().then(() => {\n        console.log('WarpSpace game initialized successfully');\n        ui.innerHTML = '<div>WarpSpace - Ready to Play</div>';\n    }).catch(error => {\n        console.error('Failed to initialize game:', error);\n        ui.innerHTML = '<div>Error: Failed to load game</div>';\n    });\n    \n    // Handle window resize\n    window.addEventListener('resize', () => {\n        game.handleResize();\n    });\n    \n    // Handle visibility change for pause/resume\n    document.addEventListener('visibilitychange', () => {\n        if (document.hidden) {\n            game.pause();\n        } else {\n            game.resume();\n        }\n    });\n});"], "names": ["Vector2", "x", "y", "other", "scalar", "mag", "angle", "magnitude", "cos", "sin", "newX", "newY", "tolerance", "InputManager", "canvas", "e", "event", "key", "button", "rect", "touch", "touchPos", "action", "keys", "movement", "joystickInput", "existing", "filtered", "k", "VirtualJoystick", "ctx", "touchId", "position", "offset", "_GameObject", "deltaTime", "interpolation", "renderPos", "tag", "target", "direction", "speed", "force", "bounds", "__publicField", "GameObject", "Projectile", "type", "owner", "i", "alpha", "currentPos", "nextPos", "renderCurrentPos", "interpolatedOffset", "velocityEnd", "margin", "color", "factor", "hex", "r", "g", "b", "WeaponSystem", "gameObjectManager", "projectile", "firePosition", "baseAngle", "angleStep", "spreadDirection", "ownerPos", "forwardOffset", "firePos", "firePos3", "offset3", "flashAlpha", "flashPos", "gradient", "pattern", "rateMs", "damage", "angleRadians", "PlayerShip", "canvasWidth", "canvasHeight", "movementInput", "accelerationForce", "leftBound", "rightBound", "topBound", "bottomBound", "shipAlpha", "shipColor", "strokeColor", "flashPhase", "flashIntensity", "thrusterIntensity", "animationPhase", "baseFlame<PERSON><PERSON>th", "flameVariation", "flame<PERSON><PERSON><PERSON>", "length", "intensity", "thrusterSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "velocityScale", "width", "height", "result", "weaponSystem", "actualDamage", "startX", "startY", "healAmount", "actualHeal", "extraLives", "color1", "color2", "hex1", "hex2", "r1", "g1", "b1", "r2", "g2", "b2", "toHex", "n", "ObjectPool", "createFn", "resetFn", "initialSize", "obj", "index", "GameObjectManager", "object", "pool", "objectToRemove", "id", "group1Tag", "group2Tag", "callback", "group1", "group2", "obj1", "obj2", "cellSize", "spatialHash", "cellX", "cellY", "dx", "dy", "nearbyObjects", "poolStats", "GameEngine", "uiElement", "currentTime", "frameTime", "projectiles", "actions", "yOffset", "stats", "size", "healthStatus", "healthBarX", "healthBarY", "healthBarWidth", "healthBarHeight", "healthWidth", "healthColor", "livesY", "lifeIconSize", "lifeIconSpacing", "lifeIconStartX", "iconX", "iconY", "invulnTime", "ui", "game", "error"], "mappings": "02BAGO,MAAMA,CAAQ,CACjB,YAAYC,EAAI,EAAGC,EAAI,EAAG,CACtB,KAAK,EAAID,EACT,KAAK,EAAIC,CACb,CAGA,OAAO,MAAO,CACV,OAAO,IAAIF,EAAQ,EAAG,CAAC,CAC3B,CAEA,OAAO,KAAM,CACT,OAAO,IAAIA,EAAQ,EAAG,CAAC,CAC3B,CAEA,OAAO,IAAK,CACR,OAAO,IAAIA,EAAQ,EAAG,EAAE,CAC5B,CAEA,OAAO,MAAO,CACV,OAAO,IAAIA,EAAQ,EAAG,CAAC,CAC3B,CAEA,OAAO,MAAO,CACV,OAAO,IAAIA,EAAQ,GAAI,CAAC,CAC5B,CAEA,OAAO,OAAQ,CACX,OAAO,IAAIA,EAAQ,EAAG,CAAC,CAC3B,CAGA,IAAIG,EAAO,CACP,OAAO,IAAIH,EAAQ,KAAK,EAAIG,EAAM,EAAG,KAAK,EAAIA,EAAM,CAAC,CACzD,CAEA,SAASA,EAAO,CACZ,OAAO,IAAIH,EAAQ,KAAK,EAAIG,EAAM,EAAG,KAAK,EAAIA,EAAM,CAAC,CACzD,CAEA,SAASC,EAAQ,CACb,OAAO,IAAIJ,EAAQ,KAAK,EAAII,EAAQ,KAAK,EAAIA,CAAM,CACvD,CAEA,OAAOA,EAAQ,CACX,GAAIA,IAAW,EAAG,MAAM,IAAI,MAAM,kBAAkB,EACpD,OAAO,IAAIJ,EAAQ,KAAK,EAAII,EAAQ,KAAK,EAAIA,CAAM,CACvD,CAGA,WAAY,CACR,OAAO,KAAK,KAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,CAAC,CACtD,CAEA,WAAY,CACR,MAAMC,EAAM,KAAK,UAAS,EAC1B,OAAIA,IAAQ,EAAUL,EAAQ,KAAI,EAC3B,KAAK,OAAOK,CAAG,CAC1B,CAGA,SAASF,EAAO,CACZ,OAAO,KAAK,SAASA,CAAK,EAAE,UAAS,CACzC,CAGA,IAAIA,EAAO,CACP,OAAO,KAAK,EAAIA,EAAM,EAAI,KAAK,EAAIA,EAAM,CAC7C,CAGA,WAAWA,EAAO,CACd,YAAK,GAAKA,EAAM,EAChB,KAAK,GAAKA,EAAM,EACT,IACX,CAEA,gBAAgBA,EAAO,CACnB,YAAK,GAAKA,EAAM,EAChB,KAAK,GAAKA,EAAM,EACT,IACX,CAEA,gBAAgBC,EAAQ,CACpB,YAAK,GAAKA,EACV,KAAK,GAAKA,EACH,IACX,CAEA,kBAAmB,CACf,MAAMC,EAAM,KAAK,UAAS,EAC1B,OAAIA,EAAM,IACN,KAAK,GAAKA,EACV,KAAK,GAAKA,GAEP,IACX,CAGA,IAAIJ,EAAGC,EAAG,CACN,YAAK,EAAID,EACT,KAAK,EAAIC,EACF,IACX,CAEA,cAAcC,EAAO,CACjB,YAAK,EAAIA,EAAM,EACf,KAAK,EAAIA,EAAM,EACR,IACX,CAGA,OAAQ,CACJ,OAAO,KAAK,MAAM,KAAK,EAAG,KAAK,CAAC,CACpC,CAEA,OAAO,UAAUG,EAAOC,EAAY,EAAG,CACnC,OAAO,IAAIP,EACP,KAAK,IAAIM,CAAK,EAAIC,EAClB,KAAK,IAAID,CAAK,EAAIC,CAC9B,CACI,CAGA,OAAOD,EAAO,CACV,MAAME,EAAM,KAAK,IAAIF,CAAK,EACpBG,EAAM,KAAK,IAAIH,CAAK,EACpBI,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAIC,EAC/BE,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAID,EACrC,OAAO,IAAIR,EAAQU,EAAMC,CAAI,CACjC,CAEA,cAAcL,EAAO,CACjB,MAAME,EAAM,KAAK,IAAIF,CAAK,EACpBG,EAAM,KAAK,IAAIH,CAAK,EACpBI,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAIC,EAC/BE,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAID,EACrC,YAAK,EAAIE,EACT,KAAK,EAAIC,EACF,IACX,CAGA,eAAgB,CACZ,OAAO,IAAIX,EAAQ,CAAC,KAAK,EAAG,KAAK,CAAC,CACtC,CAGA,OAAQ,CACJ,OAAO,IAAIA,EAAQ,KAAK,EAAG,KAAK,CAAC,CACrC,CAEA,OAAOG,EAAOS,EAAY,EAAG,CACzB,OAAIA,IAAc,EACP,KAAK,IAAMT,EAAM,GAAK,KAAK,IAAMA,EAAM,EAE3C,KAAK,IAAI,KAAK,EAAIA,EAAM,CAAC,GAAKS,GAC9B,KAAK,IAAI,KAAK,EAAIT,EAAM,CAAC,GAAKS,CACzC,CAEA,UAAW,CACP,MAAO,WAAW,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,EAAE,QAAQ,CAAC,CAAC,GAC7D,CACJ,CChKO,MAAMC,CAAa,CACtB,YAAYC,EAAQ,CAChB,KAAK,OAASA,EAGd,KAAK,KAAO,IAAI,IAChB,KAAK,YAAc,IAAI,IACvB,KAAK,aAAe,IAAI,IAExB,KAAK,cAAgB,IAAId,EAAQ,EAAG,CAAC,EACrC,KAAK,aAAe,IAAI,IACxB,KAAK,aAAe,IAAI,IACxB,KAAK,cAAgB,IAAI,IAEzB,KAAK,QAAU,IAAI,IACnB,KAAK,aAAe,IAAI,IACxB,KAAK,WAAa,IAAI,IAGtB,KAAK,YAAc,IAAI,IACvB,KAAK,qBAAoB,EAGzB,KAAK,cAAgB,iBAAkB,OACvC,KAAK,gBAAkB,KAGvB,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,iBAAmB,KAAK,iBAAiB,KAAK,IAAI,EACvD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EAGrD,KAAK,KAAI,CACb,CAEA,MAAO,CAEH,SAAS,iBAAiB,UAAW,KAAK,aAAa,EACvD,SAAS,iBAAiB,QAAS,KAAK,WAAW,EAGnD,KAAK,OAAO,iBAAiB,YAAa,KAAK,eAAe,EAC9D,KAAK,OAAO,iBAAiB,UAAW,KAAK,aAAa,EAC1D,KAAK,OAAO,iBAAiB,YAAa,KAAK,eAAe,EAG9D,KAAK,OAAO,iBAAiB,aAAc,KAAK,iBAAkB,CAAE,QAAS,GAAO,EACpF,KAAK,OAAO,iBAAiB,WAAY,KAAK,eAAgB,CAAE,QAAS,GAAO,EAChF,KAAK,OAAO,iBAAiB,YAAa,KAAK,gBAAiB,CAAE,QAAS,GAAO,EAGlF,KAAK,OAAO,iBAAiB,cAAgBe,GAAMA,EAAE,gBAAgB,EAGjE,KAAK,eACL,KAAK,oBAAmB,EAG5B,QAAQ,IAAI,0BAA0B,CAC1C,CAEA,sBAAuB,CAEnB,KAAK,YAAY,IAAI,SAAU,CAAC,UAAW,MAAM,CAAC,EAClD,KAAK,YAAY,IAAI,WAAY,CAAC,YAAa,MAAM,CAAC,EACtD,KAAK,YAAY,IAAI,WAAY,CAAC,YAAa,MAAM,CAAC,EACtD,KAAK,YAAY,IAAI,YAAa,CAAC,aAAc,MAAM,CAAC,EAGxD,KAAK,YAAY,IAAI,OAAQ,CAAC,QAAS,OAAO,CAAC,EAC/C,KAAK,YAAY,IAAI,QAAS,CAAC,SAAU,MAAM,CAAC,EAChD,KAAK,YAAY,IAAI,WAAY,CAAC,OAAQ,MAAM,CAAC,EAGjD,KAAK,YAAY,IAAI,QAAS,CAAC,IAAI,CAAC,CACxC,CAGA,cAAcC,EAAO,CACjB,MAAMC,EAAMD,EAAM,KAEb,KAAK,KAAK,IAAIC,CAAG,GAClB,KAAK,YAAY,IAAIA,EAAK,EAAI,EAGlC,KAAK,KAAK,IAAIA,EAAK,EAAI,EAGnB,KAAK,UAAUA,CAAG,GAClBD,EAAM,eAAc,CAE5B,CAEA,YAAYA,EAAO,CACf,MAAMC,EAAMD,EAAM,KAClB,KAAK,KAAK,IAAIC,EAAK,EAAK,EACxB,KAAK,aAAa,IAAIA,EAAK,EAAI,EAE3B,KAAK,UAAUA,CAAG,GAClBD,EAAM,eAAc,CAE5B,CAGA,gBAAgBA,EAAO,CACnB,MAAME,EAASF,EAAM,OAEhB,KAAK,aAAa,IAAIE,CAAM,GAC7B,KAAK,aAAa,IAAIA,EAAQ,EAAI,EAGtC,KAAK,aAAa,IAAIA,EAAQ,EAAI,EAClC,KAAK,oBAAoBF,CAAK,EAE9BA,EAAM,eAAc,CACxB,CAEA,cAAcA,EAAO,CACjB,MAAME,EAASF,EAAM,OACrB,KAAK,aAAa,IAAIE,EAAQ,EAAK,EACnC,KAAK,cAAc,IAAIA,EAAQ,EAAI,EACnC,KAAK,oBAAoBF,CAAK,EAE9BA,EAAM,eAAc,CACxB,CAEA,gBAAgBA,EAAO,CACnB,KAAK,oBAAoBA,CAAK,CAClC,CAEA,oBAAoBA,EAAO,CACvB,MAAMG,EAAO,KAAK,OAAO,sBAAqB,EAC9C,KAAK,cAAc,IACfH,EAAM,QAAUG,EAAK,KACrBH,EAAM,QAAUG,EAAK,GACjC,CACI,CAGA,iBAAiBH,EAAO,CACpBA,EAAM,eAAc,EAEpB,UAAWI,KAASJ,EAAM,eAAgB,CACtC,MAAMK,EAAW,KAAK,iBAAiBD,CAAK,EAC5C,KAAK,QAAQ,IAAIA,EAAM,WAAYC,CAAQ,EAC3C,KAAK,aAAa,IAAID,EAAM,WAAYC,EAAS,OAAO,EAGpD,KAAK,iBACL,KAAK,gBAAgB,iBAAiBD,EAAM,WAAYC,CAAQ,CAExE,CACJ,CAEA,eAAeL,EAAO,CAClBA,EAAM,eAAc,EAEpB,UAAWI,KAASJ,EAAM,eAAgB,CACtC,MAAMK,EAAW,KAAK,iBAAiBD,CAAK,EAC5C,KAAK,WAAW,IAAIA,EAAM,WAAYC,CAAQ,EAC9C,KAAK,QAAQ,OAAOD,EAAM,UAAU,EAGhC,KAAK,iBACL,KAAK,gBAAgB,eAAeA,EAAM,UAAU,CAE5D,CACJ,CAEA,gBAAgBJ,EAAO,CACnBA,EAAM,eAAc,EAEpB,UAAWI,KAASJ,EAAM,eAAgB,CACtC,MAAMK,EAAW,KAAK,iBAAiBD,CAAK,EAC5C,KAAK,QAAQ,IAAIA,EAAM,WAAYC,CAAQ,EAGvC,KAAK,iBACL,KAAK,gBAAgB,gBAAgBD,EAAM,WAAYC,CAAQ,CAEvE,CACJ,CAEA,iBAAiBD,EAAO,CACpB,MAAMD,EAAO,KAAK,OAAO,sBAAqB,EAC9C,OAAO,IAAInB,EACPoB,EAAM,QAAUD,EAAK,KACrBC,EAAM,QAAUD,EAAK,GACjC,CACI,CAGA,UAAUF,EAAK,CACX,OAAO,KAAK,KAAK,IAAIA,CAAG,GAAK,EACjC,CAEA,aAAaA,EAAK,CACd,OAAO,KAAK,YAAY,IAAIA,CAAG,GAAK,EACxC,CAEA,cAAcA,EAAK,CACf,OAAO,KAAK,aAAa,IAAIA,CAAG,GAAK,EACzC,CAEA,YAAYC,EAAS,EAAG,CACpB,OAAO,KAAK,aAAa,IAAIA,CAAM,GAAK,EAC5C,CAEA,eAAeA,EAAS,EAAG,CACvB,OAAO,KAAK,aAAa,IAAIA,CAAM,GAAK,EAC5C,CAEA,gBAAgBA,EAAS,EAAG,CACxB,OAAO,KAAK,cAAc,IAAIA,CAAM,GAAK,EAC7C,CAGA,aAAaI,EAAQ,CACjB,MAAMC,EAAO,KAAK,YAAY,IAAID,CAAM,EACxC,OAAKC,EAEEA,EAAK,KAAKN,GAAO,KAAK,UAAUA,CAAG,CAAC,EAFzB,EAGtB,CAEA,gBAAgBK,EAAQ,CACpB,MAAMC,EAAO,KAAK,YAAY,IAAID,CAAM,EACxC,OAAKC,EAEEA,EAAK,KAAKN,GAAO,KAAK,aAAaA,CAAG,CAAC,EAF5B,EAGtB,CAEA,iBAAiBK,EAAQ,CACrB,MAAMC,EAAO,KAAK,YAAY,IAAID,CAAM,EACxC,OAAKC,EAEEA,EAAK,KAAKN,GAAO,KAAK,cAAcA,CAAG,CAAC,EAF7B,EAGtB,CAGA,mBAAoB,CAChB,MAAMO,EAAW,IAAIxB,EAAQ,EAAG,CAAC,EAQjC,GANI,KAAK,aAAa,UAAU,IAAGwB,EAAS,GAAK,GAC7C,KAAK,aAAa,WAAW,IAAGA,EAAS,GAAK,GAC9C,KAAK,aAAa,QAAQ,IAAGA,EAAS,GAAK,GAC3C,KAAK,aAAa,UAAU,IAAGA,EAAS,GAAK,GAG7C,KAAK,iBAAmB,KAAK,gBAAgB,SAAQ,EAAI,CACzD,MAAMC,EAAgB,KAAK,gBAAgB,SAAQ,EACnDD,EAAS,WAAWC,CAAa,CACrC,CAGA,OAAID,EAAS,UAAS,EAAK,GACvBA,EAAS,iBAAgB,EAGtBA,CACX,CAGA,cAAcF,EAAQC,EAAM,CACxB,KAAK,YAAY,IAAID,EAAQ,MAAM,QAAQC,CAAI,EAAIA,EAAO,CAACA,CAAI,CAAC,CACpE,CAEA,cAAcD,EAAQL,EAAK,CACvB,MAAMS,EAAW,KAAK,YAAY,IAAIJ,CAAM,GAAK,CAAA,EACjDI,EAAS,KAAKT,CAAG,EACjB,KAAK,YAAY,IAAIK,EAAQI,CAAQ,CACzC,CAEA,iBAAiBJ,EAAQL,EAAK,CAE1B,MAAMU,GADW,KAAK,YAAY,IAAIL,CAAM,GAAK,CAAA,GACvB,OAAOM,GAAKA,IAAMX,CAAG,EAC/C,KAAK,YAAY,IAAIK,EAAQK,CAAQ,CACzC,CAGA,UAAUV,EAAK,CACX,UAAWM,KAAQ,KAAK,YAAY,OAAM,EACtC,GAAIA,EAAK,SAASN,CAAG,EACjB,MAAO,GAGf,MAAO,EACX,CAGA,qBAAsB,CAClB,KAAK,gBAAkB,IAAIY,EAAgB,KAAK,MAAM,CAC1D,CAGA,QAAS,CAEL,KAAK,YAAY,MAAK,EACtB,KAAK,aAAa,MAAK,EACvB,KAAK,aAAa,MAAK,EACvB,KAAK,cAAc,MAAK,EACxB,KAAK,aAAa,MAAK,EACvB,KAAK,WAAW,MAAK,EAGjB,KAAK,iBACL,KAAK,gBAAgB,OAAM,CAEnC,CAGA,OAAOC,EAAK,CACJ,KAAK,iBACL,KAAK,gBAAgB,OAAOA,CAAG,CAEvC,CAGA,SAAU,CACN,SAAS,oBAAoB,UAAW,KAAK,aAAa,EAC1D,SAAS,oBAAoB,QAAS,KAAK,WAAW,EAEtD,KAAK,OAAO,oBAAoB,YAAa,KAAK,eAAe,EACjE,KAAK,OAAO,oBAAoB,UAAW,KAAK,aAAa,EAC7D,KAAK,OAAO,oBAAoB,YAAa,KAAK,eAAe,EAEjE,KAAK,OAAO,oBAAoB,aAAc,KAAK,gBAAgB,EACnE,KAAK,OAAO,oBAAoB,WAAY,KAAK,cAAc,EAC/D,KAAK,OAAO,oBAAoB,YAAa,KAAK,eAAe,EAE7D,KAAK,iBACL,KAAK,gBAAgB,QAAO,EAGhC,QAAQ,IAAI,wBAAwB,CACxC,CACJ,CAKA,MAAMD,CAAgB,CAClB,YAAYf,EAAQ,CAChB,KAAK,OAASA,EACd,KAAK,OAAS,GACd,KAAK,QAAU,KAGf,KAAK,OAAS,IAAId,EAAQ,IAAKc,EAAO,OAAS,GAAG,EAClD,KAAK,aAAe,IAAId,EAAQ,IAAKc,EAAO,OAAS,GAAG,EACxD,KAAK,YAAc,GACnB,KAAK,SAAW,GAGhB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,UAAY,2BACjB,KAAK,UAAY,0BACrB,CAEA,iBAAiBiB,EAASC,EAAU,CAEfA,EAAS,SAAS,KAAK,MAAM,GAC9B,KAAK,aACjB,KAAK,OAAS,GACd,KAAK,QAAUD,EACf,KAAK,aAAa,cAAcC,CAAQ,EACxC,KAAK,kBAAiB,EAE9B,CAEA,gBAAgBD,EAASC,EAAU,CAC3B,KAAK,QAAU,KAAK,UAAYD,IAChC,KAAK,aAAa,cAAcC,CAAQ,EACxC,KAAK,kBAAiB,EAE9B,CAEA,eAAeD,EAAS,CAChB,KAAK,QAAU,KAAK,UAAYA,IAChC,KAAK,OAAS,GACd,KAAK,QAAU,KACf,KAAK,aAAa,cAAc,KAAK,MAAM,EAEnD,CAEA,mBAAoB,CAChB,MAAME,EAAS,KAAK,aAAa,SAAS,KAAK,MAAM,EACjDA,EAAO,YAAc,KAAK,cAC1BA,EAAO,iBAAgB,EAAG,gBAAgB,KAAK,WAAW,EAC1D,KAAK,aAAe,KAAK,OAAO,IAAIA,CAAM,EAElD,CAEA,UAAW,CACP,GAAI,CAAC,KAAK,OAAQ,OAAO,IAAIjC,EAAQ,EAAG,CAAC,EAEzC,MAAMiC,EAAS,KAAK,aAAa,SAAS,KAAK,MAAM,EAC/C1B,EAAY0B,EAAO,UAAS,EAAK,KAAK,YAE5C,OAAI1B,EAAY,KAAK,SACV,IAAIP,EAAQ,EAAG,CAAC,EAGpBiC,EAAO,YAAY,SAAS1B,CAAS,CAChD,CAEA,UAAW,CACP,OAAO,KAAK,MAChB,CAEA,QAAS,CAEL,KAAK,OAAO,IAAI,IAAK,KAAK,OAAO,OAAS,GAAG,EACxC,KAAK,QACN,KAAK,aAAa,cAAc,KAAK,MAAM,CAEnD,CAEA,OAAOuB,EAAK,CAERA,EAAI,KAAI,EACRA,EAAI,UAAY,KAAK,UACrBA,EAAI,YAAc,2BAClBA,EAAI,UAAY,EAEhBA,EAAI,UAAS,EACbA,EAAI,IAAI,KAAK,OAAO,EAAG,KAAK,OAAO,EAAG,KAAK,WAAY,EAAG,KAAK,GAAK,CAAC,EACrEA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,KAAK,UACrBA,EAAI,UAAS,EACbA,EAAI,IAAI,KAAK,aAAa,EAAG,KAAK,aAAa,EAAG,KAAK,WAAY,EAAG,KAAK,GAAK,CAAC,EACjFA,EAAI,KAAI,EACRA,EAAI,OAAM,EAEVA,EAAI,QAAO,CACf,CAEA,SAAU,CAEV,CACJ,CChcO,MAAMI,EAAN,MAAMA,CAAW,CACpB,YAAYjC,EAAI,EAAGC,EAAI,EAAG,CACtB,KAAK,SAAW,IAAIF,EAAQC,EAAGC,CAAC,EAChC,KAAK,SAAW,IAAIF,EAAQ,EAAG,CAAC,EAChC,KAAK,aAAe,IAAIA,EAAQ,EAAG,CAAC,EAGpC,KAAK,SAAW,EAChB,KAAK,MAAQ,IAAIA,EAAQ,EAAG,CAAC,EAG7B,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,GAGjB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,CAAE,EAAG,EAAG,EAAG,EAAG,MAAO,EAAG,OAAQ,CAAC,EAGxD,KAAK,GAAKkC,EAAW,WAAU,EAG/B,KAAK,KAAO,IAAI,GACpB,CAIA,OAAO,YAAa,CAChB,MAAO,EAAEA,EAAW,SACxB,CAGA,OAAOC,EAAW,CACT,KAAK,SAGV,KAAK,SAAS,WAAW,KAAK,aAAa,SAASA,EAAY,GAAI,CAAC,EACrE,KAAK,SAAS,WAAW,KAAK,SAAS,SAASA,EAAY,GAAI,CAAC,EAGjE,KAAK,sBAAqB,EAC9B,CAGA,OAAOL,EAAKM,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAS,OAGnB,MAAMC,EAAY,KAAK,SAAS,IAAI,KAAK,SAAS,SAASD,EAAgB,GAAI,CAAC,EAGhFN,EAAI,KAAI,EACRA,EAAI,UAAUO,EAAU,EAAGA,EAAU,CAAC,EACtCP,EAAI,OAAO,KAAK,QAAQ,EACxBA,EAAI,MAAM,KAAK,MAAM,EAAG,KAAK,MAAM,CAAC,EAGhC,KAAK,gBAAkB,IACvBA,EAAI,YAAc,UAClBA,EAAI,UAAS,EACbA,EAAI,IAAI,EAAG,EAAG,KAAK,gBAAiB,EAAG,KAAK,GAAK,CAAC,EAClDA,EAAI,OAAM,GAGdA,EAAI,QAAO,CACf,CAGA,uBAAwB,CACpB,KAAK,gBAAgB,EAAI,KAAK,SAAS,EAAI,KAAK,gBAChD,KAAK,gBAAgB,EAAI,KAAK,SAAS,EAAI,KAAK,gBAChD,KAAK,gBAAgB,MAAQ,KAAK,gBAAkB,EACpD,KAAK,gBAAgB,OAAS,KAAK,gBAAkB,CACzD,CAGA,aAAa3B,EAAO,CAChB,MAAI,CAAC,KAAK,QAAU,CAACA,EAAM,OAAe,GAGtC,KAAK,gBAAkB,GAAKA,EAAM,gBAAkB,EACnC,KAAK,SAAS,SAASA,EAAM,QAAQ,EACnC,KAAK,gBAAkBA,EAAM,gBAG7C,EACX,CAGA,SAAU,CACN,KAAK,UAAY,GACjB,KAAK,OAAS,GACd,KAAK,QAAU,EACnB,CAEA,OAAQ,CACJ,KAAK,SAAS,IAAI,EAAG,CAAC,EACtB,KAAK,SAAS,IAAI,EAAG,CAAC,EACtB,KAAK,aAAa,IAAI,EAAG,CAAC,EAC1B,KAAK,SAAW,EAChB,KAAK,MAAM,IAAI,EAAG,CAAC,EACnB,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,GACjB,KAAK,KAAK,MAAK,CACnB,CAGA,OAAOmC,EAAK,CACR,KAAK,KAAK,IAAIA,CAAG,CACrB,CAEA,UAAUA,EAAK,CACX,KAAK,KAAK,OAAOA,CAAG,CACxB,CAEA,OAAOA,EAAK,CACR,OAAO,KAAK,KAAK,IAAIA,CAAG,CAC5B,CAGA,WAAWnC,EAAO,CACd,OAAO,KAAK,SAAS,SAASA,EAAM,QAAQ,CAChD,CAEA,YAAYA,EAAO,CACf,OAAOA,EAAM,SAAS,SAAS,KAAK,QAAQ,EAAE,UAAS,CAC3D,CAEA,OAAOoC,EAAQ,CACX,MAAMC,EAAY,KAAK,YAAYD,CAAM,EACzC,KAAK,SAAWC,EAAU,MAAK,CACnC,CAGA,YAAYD,EAAQE,EAAON,EAAW,CAElC,MAAMX,EADY,KAAK,YAAYe,CAAM,EACd,SAASE,EAAQN,EAAY,GAAI,EAC5D,KAAK,SAAS,WAAWX,CAAQ,CACrC,CAEA,WAAWkB,EAAO,CACd,KAAK,aAAa,WAAWA,CAAK,CACtC,CAGA,cAAcC,EAAQ,CAClB,OAAO,KAAK,SAAS,EAAIA,EAAO,MACzB,KAAK,SAAS,EAAIA,EAAO,OACzB,KAAK,SAAS,EAAIA,EAAO,KACzB,KAAK,SAAS,EAAIA,EAAO,MACpC,CAEA,iBAAiBA,EAAQ,CACjB,KAAK,SAAS,EAAIA,EAAO,OAAM,KAAK,SAAS,EAAIA,EAAO,OACxD,KAAK,SAAS,EAAIA,EAAO,QAAO,KAAK,SAAS,EAAIA,EAAO,MACzD,KAAK,SAAS,EAAIA,EAAO,MAAK,KAAK,SAAS,EAAIA,EAAO,QACvD,KAAK,SAAS,EAAIA,EAAO,SAAQ,KAAK,SAAS,EAAIA,EAAO,IAClE,CAEA,cAAcA,EAAQ,CAClB,KAAK,SAAS,EAAI,KAAK,IAAIA,EAAO,KAAM,KAAK,IAAIA,EAAO,MAAO,KAAK,SAAS,CAAC,CAAC,EAC/E,KAAK,SAAS,EAAI,KAAK,IAAIA,EAAO,IAAK,KAAK,IAAIA,EAAO,OAAQ,KAAK,SAAS,CAAC,CAAC,CACnF,CACJ,EA1IIC,EA3BSV,EA2BF,YAAY,GA3BhB,IAAMW,EAANX,ECCA,MAAMY,UAAmBD,CAAW,CACvC,YAAY5C,EAAI,EAAGC,EAAI,EAAG,CACtB,MAAMD,EAAGC,CAAC,EAGV,KAAK,MAAQ,IACb,KAAK,OAAS,EACd,KAAK,SAAW,IAChB,KAAK,IAAM,EAGX,KAAK,MAAQ,EACb,KAAK,OAAS,GACd,KAAK,gBAAkB,EACvB,KAAK,MAAQ,UACb,KAAK,WAAa,UAGlB,KAAK,eAAiB,CAAA,EACtB,KAAK,eAAiB,EACtB,KAAK,cAAgB,GAGrB,KAAK,KAAO,SACZ,KAAK,MAAQ,KAGb,KAAK,OAAO,YAAY,EAGxB,KAAK,OAAS,GACd,KAAK,QAAU,EACnB,CAUA,WAAW8B,EAAUQ,EAAWC,EAAQ,IAAKM,EAAO,SAAUC,EAAQ,KAAM,CACxE,YAAK,SAAS,cAAchB,CAAQ,EACpC,KAAK,SAAWQ,EAAU,UAAS,EAAG,SAASC,CAAK,EACpD,KAAK,MAAQA,EACb,KAAK,KAAOM,EACZ,KAAK,MAAQC,EACb,KAAK,IAAM,EAGX,KAAK,eAAiB,CAAA,EAGtB,KAAK,mBAAkB,EAGvB,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,GAGjB,KAAK,KAAK,MAAK,EACf,KAAK,OAAO,YAAY,EACxB,KAAK,OAAOD,EAAO,YAAY,EAExB,IACX,CAKA,oBAAqB,CACjB,OAAQ,KAAK,KAAI,CACb,IAAK,SACD,KAAK,MAAQ,UACb,KAAK,WAAa,UAClB,KAAK,MAAQ,EACb,KAAK,OAAS,GACd,MACJ,IAAK,QACD,KAAK,MAAQ,UACb,KAAK,WAAa,UAClB,KAAK,MAAQ,EACb,KAAK,OAAS,EACd,MACJ,QACI,KAAK,MAAQ,UACb,KAAK,WAAa,UAClB,KAChB,CACI,CAMA,OAAOZ,EAAW,CACd,GAAK,KAAK,OAMV,IAHA,KAAK,KAAOA,EAGR,KAAK,KAAO,KAAK,SAAU,CAC3B,KAAK,QAAO,EACZ,MACJ,CAGA,KAAK,YAAW,EAGhB,MAAM,OAAOA,CAAS,EAGtB,KAAK,sBAAqB,EAC9B,CAKA,aAAc,CAEV,KAAK,eAAe,QAAQ,KAAK,SAAS,MAAK,CAAE,EAG7C,KAAK,eAAe,OAAS,KAAK,gBAClC,KAAK,eAAe,IAAG,CAE/B,CAOA,OAAOL,EAAKM,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAS,OAGnB,MAAMC,EAAY,KAAK,SAAS,IAAI,KAAK,SAAS,SAASD,EAAgB,GAAI,CAAC,EAEhFN,EAAI,KAAI,EAGR,KAAK,YAAYA,EAAKM,CAAa,EAGnC,KAAK,iBAAiBN,EAAKO,CAAS,EAGhC,OAAO,YACP,KAAK,gBAAgBP,EAAKO,CAAS,EAGvCP,EAAI,QAAO,CACf,CAOA,YAAYA,EAAKM,EAAe,CAC5B,GAAI,OAAK,eAAe,OAAS,GAEjC,CAAAN,EAAI,YAAc,KAAK,WACvBA,EAAI,UAAY,EAChBA,EAAI,QAAU,QAGd,QAASmB,EAAI,EAAGA,EAAI,KAAK,eAAe,OAAS,EAAGA,IAAK,CACrD,MAAMC,EAAQ,KAAK,IAAI,KAAK,cAAeD,CAAC,EACtCE,EAAa,KAAK,eAAeF,CAAC,EAClCG,EAAU,KAAK,eAAeH,EAAI,CAAC,EAGzC,IAAII,EAAmBF,EACvB,GAAIF,IAAM,EAAG,CACT,MAAMK,EAAqB,KAAK,SAAS,SAASlB,EAAgB,GAAI,EACtEiB,EAAmBF,EAAW,IAAIG,CAAkB,CACxD,CAEAxB,EAAI,YAAcoB,EAClBpB,EAAI,UAAS,EACbA,EAAI,OAAOuB,EAAiB,EAAGA,EAAiB,CAAC,EACjDvB,EAAI,OAAOsB,EAAQ,EAAGA,EAAQ,CAAC,EAC/BtB,EAAI,OAAM,CACd,CAEAA,EAAI,YAAc,EACtB,CAOA,iBAAiBA,EAAKO,EAAW,CAC7BP,EAAI,UAAUO,EAAU,EAAGA,EAAU,CAAC,EAGtCP,EAAI,UAAY,KAAK,MACrBA,EAAI,SAAS,CAAC,KAAK,MAAQ,EAAG,CAAC,KAAK,OAAS,EAAG,KAAK,MAAO,KAAK,MAAM,EAGvEA,EAAI,UAAY,UAChBA,EAAI,SAAS,GAAI,CAAC,KAAK,OAAS,EAAG,EAAG,KAAK,MAAM,CACrD,CAMA,qBAAqBA,EAAK,CAEtBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,CAAC,EAC9BA,EAAI,OAAO,KAAK,MAAQ,EAAG,CAAC,EAC5BA,EAAI,OAAO,EAAG,KAAK,OAAS,CAAC,EAC7BA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,CAAC,EAC7BA,EAAI,UAAS,EACbA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,KAAK,aAAa,KAAK,MAAO,EAAG,EACjDA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,CAAC,EAC9BA,EAAI,OAAO,KAAK,MAAQ,EAAG,CAAC,EAC5BA,EAAI,OAAO,EAAG,KAAK,OAAS,CAAC,EAC7BA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,CAAC,EAC7BA,EAAI,UAAS,EACbA,EAAI,KAAI,CACZ,CAMA,oBAAoBA,EAAK,CAErBA,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACpEA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,KAAK,aAAa,KAAK,MAAO,EAAG,EACjDA,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,CAAC,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACnFA,EAAI,KAAI,CACZ,CAOA,gBAAgBA,EAAKO,EAAW,CAC5BP,EAAI,eAAc,EAGlBA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,IAAIO,EAAU,EAAGA,EAAU,EAAG,KAAK,gBAAiB,EAAG,KAAK,GAAK,CAAC,EACtEP,EAAI,OAAM,EAGVA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,OAAOO,EAAU,EAAGA,EAAU,CAAC,EACnC,MAAMkB,EAAclB,EAAU,IAAI,KAAK,SAAS,SAAS,GAAI,CAAC,EAC9DP,EAAI,OAAOyB,EAAY,EAAGA,EAAY,CAAC,EACvCzB,EAAI,OAAM,EAGVA,EAAI,UAAY,UAChBA,EAAI,KAAO,aACXA,EAAI,SAAS,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,KAAMO,EAAU,EAAI,GAAIA,EAAU,EAAI,EAAE,CAChF,CAOA,cAAcM,EAAQ,CAClB,MAAMa,EAAS,KAAK,IAAI,KAAK,MAAO,KAAK,MAAM,EAC/C,OAAO,KAAK,SAAS,EAAIb,EAAO,KAAOa,GAChC,KAAK,SAAS,EAAIb,EAAO,MAAQa,GACjC,KAAK,SAAS,EAAIb,EAAO,IAAMa,GAC/B,KAAK,SAAS,EAAIb,EAAO,OAASa,CAC7C,CAMA,YAAYrD,EAAO,CAEf,KAAK,QAAO,CAChB,CAKA,OAAQ,CACJ,MAAM,MAAK,EACX,KAAK,IAAM,EACX,KAAK,eAAiB,CAAA,EACtB,KAAK,KAAO,SACZ,KAAK,MAAQ,KACb,KAAK,MAAQ,IACb,KAAK,OAAS,EACd,KAAK,SAAW,GACpB,CAQA,YAAYsD,EAAOC,EAAQ,CAEvB,MAAMC,EAAMF,EAAM,QAAQ,IAAK,EAAE,EAC3BG,EAAI,KAAK,MAAM,SAASD,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,EAC5DG,EAAI,KAAK,MAAM,SAASF,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,EAC5DI,EAAI,KAAK,MAAM,SAASH,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,EAClE,MAAO,OAAOE,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAC/B,CAQA,aAAaL,EAAOC,EAAQ,CACxB,MAAMC,EAAMF,EAAM,QAAQ,IAAK,EAAE,EAC3BG,EAAI,KAAK,IAAI,IAAK,KAAK,MAAM,SAASD,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,CAAC,EAC3EG,EAAI,KAAK,IAAI,IAAK,KAAK,MAAM,SAASF,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,CAAC,EAC3EI,EAAI,KAAK,IAAI,IAAK,KAAK,MAAM,SAASH,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,CAAC,EACjF,MAAO,OAAOE,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAC/B,CACJ,CC7VO,MAAMC,CAAa,CACtB,YAAYf,EAAOgB,EAAmB,CAClC,KAAK,MAAQhB,EACb,KAAK,kBAAoBgB,EAGzB,KAAK,SAAW,IAChB,KAAK,aAAe,EACpB,KAAK,QAAU,GAGf,KAAK,gBAAkB,IACvB,KAAK,iBAAmB,EACxB,KAAK,mBAAqB,IAC1B,KAAK,eAAiB,SAGtB,KAAK,eAAiB,SACtB,KAAK,YAAc,KAAK,GAAK,EAG7B,KAAK,oBAAsB,IAC3B,KAAK,gBAAkB,EACvB,KAAK,qBAAuB,CAAA,EAG5B,KAAK,UAAY,KACjB,KAAK,YAAc,GAGnB,KAAK,yBAAwB,EAE7B,QAAQ,IAAI,sCAAuChB,EAAM,YAAY,IAAI,CAC7E,CAKA,0BAA2B,CAElB,KAAK,kBAAkB,MAAM,IAAI,YAAY,GAC9C,KAAK,kBAAkB,WACnB,aACA,IAAM,IAAIF,EACTmB,GAAeA,EAAW,MAAK,EAChC,EAChB,CAEI,CAMA,OAAO9B,EAAW,CAET,KAAK,UACN,KAAK,cAAgBA,EACjB,KAAK,cAAgB,KAAK,WAC1B,KAAK,QAAU,GACf,KAAK,aAAe,IAKxB,KAAK,gBAAkB,IACvB,KAAK,iBAAmBA,EACpB,KAAK,iBAAmB,IACxB,KAAK,qBAAuB,CAAA,GAGxC,CAOA,KAAKK,EAAYxC,EAAQ,KAAM,CAE3B,GADA,QAAQ,IAAI,uCAAwC,KAAK,OAAO,EAC5D,CAAC,KAAK,QACN,eAAQ,IAAI,kCAAkC,EACvC,GAIX,OAAQ,KAAK,eAAc,CACvB,IAAK,SACD,KAAK,WAAWwC,CAAS,EACzB,MACJ,IAAK,SACD,KAAK,WAAWA,CAAS,EACzB,MACJ,IAAK,SACD,KAAK,WAAWA,CAAS,EACzB,MACJ,IAAK,SACD,KAAK,WAAWA,CAAS,EACzB,MACJ,QACI,KAAK,WAAWA,CAAS,EACzB,KAChB,CAGQ,YAAK,QAAU,GACf,KAAK,aAAe,EAGpB,KAAK,mBAAkB,EAGvB,KAAK,cAAa,EAEX,EACX,CAMA,WAAWA,EAAW,CAClB,MAAM0B,EAAe,KAAK,gBAAe,EACzC,KAAK,iBAAiBA,EAAc1B,CAAS,CACjD,CAMA,WAAWA,EAAW,CAClB,MAAM0B,EAAe,KAAK,gBAAe,EACnCjC,EAASO,EAAU,cAAa,EAAG,SAAS,CAAC,EAGnD,KAAK,iBAAiB0B,EAAa,SAASjC,CAAM,EAAGO,CAAS,EAE9D,KAAK,iBAAiB0B,EAAa,IAAIjC,CAAM,EAAGO,CAAS,CAC7D,CAMA,WAAWA,EAAW,CAClB,MAAM0B,EAAe,KAAK,gBAAe,EACnCjC,EAASO,EAAU,cAAa,EAAG,SAAS,EAAE,EAGpD,KAAK,iBAAiB0B,EAAc1B,CAAS,EAE7C,KAAK,iBAAiB0B,EAAa,SAASjC,CAAM,EAAGO,CAAS,EAE9D,KAAK,iBAAiB0B,EAAa,IAAIjC,CAAM,EAAGO,CAAS,CAC7D,CAMA,WAAWA,EAAW,CAClB,MAAM0B,EAAe,KAAK,gBAAe,EACnCC,EAAY3B,EAAU,MAAK,EAC3B4B,EAAY,KAAK,YAAc,EAGrC,QAASnB,EAAI,GAAIA,GAAK,EAAGA,IAAK,CAC1B,MAAM3C,EAAQ6D,EAAalB,EAAImB,EACzBC,EAAkBrE,EAAQ,UAAUM,CAAK,EAC/C,KAAK,iBAAiB4D,EAAcG,CAAe,CACvD,CACJ,CAOA,iBAAiBrC,EAAUQ,EAAW,CAElC,MAAMyB,EAAa,IAAInB,EAGvBmB,EAAW,WACPjC,EACAQ,EACA,KAAK,gBACL,KAAK,eACL,KAAK,KACjB,EAGQyB,EAAW,OAAS,KAAK,iBACzBA,EAAW,SAAW,KAAK,mBAG3B,KAAK,kBAAkB,IAAIA,CAAU,EAErC,QAAQ,IAAI,yBAA0BA,EAAW,SAAS,SAAQ,EAAI,iBAAkBA,EAAW,SAAS,SAAQ,CAAE,CAC1H,CAMA,iBAAkB,CAEd,MAAMK,EAAW,KAAK,MAAM,SAAS,MAAK,EAGpCC,EAAgB,IAAIvE,EAAQ,EAAG,CAAC,KAAK,MAAM,OAAS,EAAI,CAAC,EAE/D,OAAOsE,EAAS,IAAIC,CAAa,CACrC,CAKA,oBAAqB,CAMjB,OALA,KAAK,gBAAkB,KAAK,oBAG5B,KAAK,qBAAuB,CAAA,EAEpB,KAAK,eAAc,CACvB,IAAK,SACD,KAAK,qBAAqB,KAAK,KAAK,gBAAe,CAAE,EACrD,MACJ,IAAK,SACD,MAAMC,EAAU,KAAK,gBAAe,EAC9BvC,EAAS,IAAIjC,EAAQ,EAAG,CAAC,EAC/B,KAAK,qBAAqB,KAAKwE,EAAQ,SAASvC,CAAM,CAAC,EACvD,KAAK,qBAAqB,KAAKuC,EAAQ,IAAIvC,CAAM,CAAC,EAClD,MACJ,IAAK,SACD,MAAMwC,EAAW,KAAK,gBAAe,EAC/BC,EAAU,IAAI1E,EAAQ,GAAI,CAAC,EACjC,KAAK,qBAAqB,KAAKyE,CAAQ,EACvC,KAAK,qBAAqB,KAAKA,EAAS,SAASC,CAAO,CAAC,EACzD,KAAK,qBAAqB,KAAKD,EAAS,IAAIC,CAAO,CAAC,EACpD,MACJ,IAAK,SAED,KAAK,qBAAqB,KAAK,KAAK,gBAAe,CAAE,EACrD,KAChB,CACI,CAKA,eAAgB,CAER,KAAK,WAAa,OAAO,KAAK,UAAU,MAAS,aACjD,KAAK,UAAU,OAAS,KAAK,YAC7B,KAAK,UAAU,YAAc,EAC7B,KAAK,UAAU,OAAO,MAAM3D,GAAK,CAE7B,QAAQ,KAAK,6BAA8BA,CAAC,CAChD,CAAC,EAET,CAMA,OAAOe,EAAK,CACJ,KAAK,gBAAkB,GACvB,KAAK,kBAAkBA,CAAG,CAElC,CAMA,kBAAkBA,EAAK,CACnB,MAAM6C,EAAa,KAAK,gBAAkB,KAAK,oBAE/C7C,EAAI,KAAI,EACRA,EAAI,YAAc6C,EAElB,UAAWC,KAAY,KAAK,qBAAsB,CAE9C,MAAMC,EAAW/C,EAAI,qBACjB8C,EAAS,EAAGA,EAAS,EAAG,EACxBA,EAAS,EAAGA,EAAS,EAAG,EACxC,EACYC,EAAS,aAAa,EAAG,SAAS,EAClCA,EAAS,aAAa,GAAK,SAAS,EACpCA,EAAS,aAAa,GAAK,SAAS,EACpCA,EAAS,aAAa,EAAG,uBAAuB,EAEhD/C,EAAI,UAAY+C,EAChB/C,EAAI,UAAS,EACbA,EAAI,IAAI8C,EAAS,EAAGA,EAAS,EAAG,GAAI,EAAG,KAAK,GAAK,CAAC,EAClD9C,EAAI,KAAI,EAGRA,EAAI,UAAY,UAChBA,EAAI,UAAS,EACbA,EAAI,IAAI8C,EAAS,EAAGA,EAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACjD9C,EAAI,KAAI,CACZ,CAEAA,EAAI,QAAO,CACf,CAMA,WAAWgD,EAAS,CACM,CAAC,SAAU,SAAU,SAAU,QAAQ,EAC3C,SAASA,CAAO,GAC9B,KAAK,eAAiBA,EACtB,QAAQ,IAAI,8BAA8BA,CAAO,EAAE,GAEnD,QAAQ,KAAK,2BAA2BA,CAAO,EAAE,CAEzD,CAMA,YAAYC,EAAQ,CAChB,KAAK,SAAW,KAAK,IAAI,GAAIA,CAAM,CACvC,CAMA,mBAAmBtC,EAAO,CACtB,KAAK,gBAAkB,KAAK,IAAI,IAAKA,CAAK,CAC9C,CAMA,oBAAoBuC,EAAQ,CACxB,KAAK,iBAAmB,KAAK,IAAI,EAAGA,CAAM,CAC9C,CAMA,eAAeC,EAAc,CACzB,KAAK,YAAc,KAAK,IAAI,EAAG,KAAK,IAAI,KAAK,GAAIA,CAAY,CAAC,CAClE,CAMA,SAAU,CACN,OAAO,KAAK,OAChB,CAMA,qBAAsB,CAClB,OAAI,KAAK,QAAgB,EAClB,KAAK,aAAe,KAAK,QACpC,CAKA,eAAgB,CACZ,KAAK,QAAU,GACf,KAAK,aAAe,CACxB,CAMA,UAAW,CACP,MAAO,CACH,QAAS,KAAK,eACd,SAAU,KAAK,SACf,gBAAiB,KAAK,gBACtB,iBAAkB,KAAK,iBACvB,QAAS,KAAK,QACd,iBAAkB,KAAK,oBAAmB,CACtD,CACI,CACJ,CCzYO,MAAMC,UAAmBrC,CAAW,CACvC,YAAY5C,EAAGC,EAAGiF,EAAaC,EAAcpB,EAAoB,KAAM,CACnE,MAAM/D,EAAGC,CAAC,EAGV,KAAK,YAAciF,EACnB,KAAK,aAAeC,EAGpB,KAAK,SAAW,IAChB,KAAK,aAAe,IACpB,KAAK,SAAW,IAGhB,KAAK,UAAY,IACjB,KAAK,OAAS,KAAK,UACnB,KAAK,SAAW,EAChB,KAAK,MAAQ,KAAK,SAClB,KAAK,eAAiB,GACtB,KAAK,wBAA0B,IAC/B,KAAK,qBAAuB,EAC5B,KAAK,YAAc,GAGnB,KAAK,iBAAmB,EACxB,KAAK,oBAAsB,IAC3B,KAAK,WAAa,GAGlB,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,gBAAkB,GAGvB,KAAK,gBAAkB,KAAK,IAAI,KAAK,MAAO,KAAK,MAAM,EAAI,EAG3D,KAAK,cAAgB,EACrB,KAAK,uBAAyB,EAG9B,KAAK,SAAW,GAChB,KAAK,cAAgB,IAAIpF,EAAQ,EAAG,CAAC,EAGrC,KAAK,aAAe,KAChBgE,IACA,KAAK,aAAe,IAAID,EAAa,KAAMC,CAAiB,GAIhE,KAAK,OAAO,QAAQ,EAEpB,QAAQ,IAAI,kCAAmC,KAAK,SAAS,SAAQ,CAAE,CAC3E,CAOA,OAAO7B,EAAWkD,EAAgB,IAAIrF,EAAQ,EAAG,CAAC,EAAG,CACjD,GAAK,KAAK,OAOV,IAJA,KAAK,cAAgBqF,EAAc,MAAK,EACxC,KAAK,SAAWA,EAAc,UAAS,EAAK,GAGxC,KAAK,SAAU,CAMf,MAAMC,EAJiBD,EAAc,SAAS,KAAK,QAAQ,EAGvB,SAAS,KAAK,QAAQ,EACnB,SAAS,KAAK,aAAelD,EAAY,GAAI,EAEpF,KAAK,SAAS,WAAWmD,CAAiB,EAGtC,KAAK,SAAS,UAAS,EAAK,KAAK,WACjC,KAAK,SAAW,KAAK,SAAS,UAAS,EAAG,SAAS,KAAK,QAAQ,EAExE,MAEI,KAAK,SAAS,gBAAgB,KAAK,IAAI,KAAK,SAAUnD,EAAY,KAAK,CAAC,EAGpE,KAAK,SAAS,UAAS,EAAK,GAC5B,KAAK,SAAS,IAAI,EAAG,CAAC,EAK9B,KAAK,SAAS,WAAW,KAAK,SAAS,SAASA,EAAY,GAAI,CAAC,EAGjE,KAAK,gBAAe,EAGpB,KAAK,eAAiBA,EAAY,IAGlC,KAAK,mBAAmBA,CAAS,EAGjC,KAAK,sBAAqB,EAGtB,KAAK,cACL,KAAK,aAAa,OAAOA,CAAS,EAE1C,CAMA,iBAAkB,CACd,MAAMoD,EAAY,KAAK,gBACjBC,EAAa,KAAK,YAAc,KAAK,gBACrCC,EAAW,KAAK,gBAChBC,EAAc,KAAK,aAAe,KAAK,gBAKzC,KAAK,SAAS,EAAIH,GAClB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,GAEtC,KAAK,SAAS,EAAIC,IACzB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,GAK7C,KAAK,SAAS,EAAIC,GAClB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,GAEtC,KAAK,SAAS,EAAIC,IACzB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,EAQrD,CAOA,OAAO5D,EAAKM,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAS,OAGnB,MAAMC,EAAY,KAAK,SAAS,IAAI,KAAK,SAAS,SAASD,EAAgB,GAAI,CAAC,EAEhFN,EAAI,KAAI,EACRA,EAAI,UAAUO,EAAU,EAAGA,EAAU,CAAC,EACtCP,EAAI,OAAO,KAAK,QAAQ,EAGxB,KAAK,aAAaA,CAAG,EAGjB,KAAK,UACL,KAAK,oBAAoBA,CAAG,EAI5B,OAAO,YACP,KAAK,cAAcA,CAAG,EAG1BA,EAAI,QAAO,EAGP,KAAK,cACL,KAAK,aAAa,OAAOA,CAAG,CAEpC,CAMA,aAAaA,EAAK,CAEd,IAAI6D,EAAY,EACZC,EAAY,UACZC,EAAc,UAGlB,GAAI,KAAK,eAAgB,CAErB,MAAMC,EAAa,KAAK,IAAI,KAAK,qBAAuB,EAAa,KAAK,GAAK,GAAI,EACnFH,EAAY,GAAM,GAAM,KAAK,IAAIG,CAAU,CAC/C,CAGA,GAAI,KAAK,WAAY,CACjB,MAAMC,EAAiB,KAAK,iBAAmB,KAAK,oBACpDH,EAAY,KAAK,iBAAiB,UAAW,UAAWG,CAAc,EACtEF,EAAc,KAAK,iBAAiB,UAAW,UAAWE,CAAc,CAC5E,CAGAjE,EAAI,YAAc6D,EAGlB7D,EAAI,UAAY8D,EAChB9D,EAAI,YAAc+D,EAClB/D,EAAI,UAAY,EAEhBA,EAAI,UAAS,EAEbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,CAAC,EAC9BA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC3CA,EAAI,OAAO,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC1CA,EAAI,UAAS,EACbA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,UAChBA,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,CAAC,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACnFA,EAAI,KAAI,EAGRA,EAAI,UAAY,UAChBA,EAAI,SAAS,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC9EA,EAAI,SAAS,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAG7EA,EAAI,UAAY,UAChBA,EAAI,UAAS,EACbA,EAAI,QAAQ,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACrEA,EAAI,QAAQ,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACpEA,EAAI,KAAI,EAGRA,EAAI,YAAc,CACtB,CAMA,oBAAoBA,EAAK,CAErB,MAAMkE,EAAoB,KAAK,SAAS,UAAS,EAAK,KAAK,SACrDC,EAAiB,KAAK,IAAI,KAAK,cAAgB,KAAK,uBAAyB,KAAK,GAAK,CAAC,EAGxFC,EAAkB,GAAKF,EACvBG,EAAiB,EAAIF,EAAiBD,EACtCI,EAAcF,EAAkBC,EAElCC,EAAc,IAEd,KAAK,kBAAkBtE,EAAK,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAGsE,CAAW,EAGzE,KAAK,kBAAkBtE,EAAK,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAGsE,CAAW,GAI5E,KAAK,yBAAyBtE,EAAKkE,EAAmBC,CAAc,CACxE,CASA,kBAAkBnE,EAAK7B,EAAGC,EAAGmG,EAAQ,CACjC,MAAMxB,EAAW/C,EAAI,qBAAqB7B,EAAGC,EAAGD,EAAGC,EAAImG,CAAM,EAC7DxB,EAAS,aAAa,EAAG,SAAS,EAClCA,EAAS,aAAa,GAAK,SAAS,EACpCA,EAAS,aAAa,EAAG,oBAAoB,EAE7C/C,EAAI,UAAY+C,EAChB/C,EAAI,UAAS,EACbA,EAAI,OAAO7B,EAAI,EAAGC,CAAC,EACnB4B,EAAI,OAAO7B,EAAI,EAAGC,CAAC,EACnB4B,EAAI,OAAO7B,EAAI,EAAGC,EAAImG,CAAM,EAC5BvE,EAAI,OAAO7B,EAAI,EAAGC,EAAImG,CAAM,EAC5BvE,EAAI,UAAS,EACbA,EAAI,KAAI,CACZ,CAQA,yBAAyBA,EAAKwE,EAAWL,EAAgB,CACrD,MAAMM,EAAe,EAAID,EACnBpD,EAAQ,GAAMoD,EAgBpB,GAbI,KAAK,IAAI,KAAK,cAAc,CAAC,EAAI,KACjCxE,EAAI,UAAY,uBAAuBoB,CAAK,IAExC,KAAK,cAAc,EAAI,EAEvBpB,EAAI,SAAS,CAAC,KAAK,MAAQ,EAAIyE,EAAc,GAAIA,EAAc,CAAC,EAGhEzE,EAAI,SAAS,KAAK,MAAQ,EAAG,GAAIyE,EAAc,CAAC,GAKpD,KAAK,cAAc,EAAI,IAAM,CAC7BzE,EAAI,UAAY,qBAAqBoB,CAAK,IAC1C,MAAMsD,EAAwB,EAAIF,GAAa,EAAI,GAAML,GACzDnE,EAAI,SAAS,GAAI,CAAC,KAAK,OAAS,EAAI0E,EAAuB,EAAGA,CAAqB,CACvF,CACJ,CAMA,cAAc1E,EAAK,CASf,GAPAA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,IAAI,EAAG,EAAG,KAAK,gBAAiB,EAAG,KAAK,GAAK,CAAC,EAClDA,EAAI,OAAM,EAGN,KAAK,SAAS,UAAS,EAAK,EAAG,CAC/BA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,EACf,MAAM2E,EAAgB,GACtB3E,EAAI,OAAO,KAAK,SAAS,EAAI2E,EAAe,KAAK,SAAS,EAAIA,CAAa,EAC3E3E,EAAI,OAAM,CACd,CAGAA,EAAI,UAAY,UAChBA,EAAI,SAAS,GAAI,GAAI,EAAG,CAAC,CAC7B,CAMA,iBAAkB,CACd,OAAO,KAAK,SAAS,UAAS,CAClC,CAMA,mBAAoB,CAChB,MAAMyD,EAAY,KAAK,gBACjBC,EAAa,KAAK,YAAc,KAAK,gBACrCC,EAAW,KAAK,gBAChBC,EAAc,KAAK,aAAe,KAAK,gBAE7C,MAAO,CACH,KAAM,KAAK,SAAS,GAAKH,EACzB,MAAO,KAAK,SAAS,GAAKC,EAC1B,IAAK,KAAK,SAAS,GAAKC,EACxB,OAAQ,KAAK,SAAS,GAAKC,CACvC,CACI,CAOA,gBAAgBzF,EAAGC,EAAG,CAClB,KAAK,SAAS,IAAID,EAAGC,CAAC,EACtB,KAAK,SAAS,IAAI,EAAG,CAAC,EACtB,KAAK,SAAW,EAChB,KAAK,cAAgB,EACrB,KAAK,SAAW,GAChB,KAAK,cAAc,IAAI,EAAG,CAAC,EAC3B,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,EAGrB,CAOA,uBAAuBwG,EAAOC,EAAQ,CAClC,KAAK,YAAcD,EACnB,KAAK,aAAeC,EAGpB,KAAK,gBAAe,CACxB,CAOA,KAAKnE,EAAYxC,EAAQ,KAAM,CAE3B,GADA,QAAQ,IAAI,iDAAkD,CAAC,CAAC,KAAK,YAAY,EAC7E,KAAK,aAAc,CACnB,MAAM4G,EAAS,KAAK,aAAa,KAAKpE,CAAS,EAC/C,eAAQ,IAAI,gCAAiCoE,CAAM,EAC5CA,CACX,CACA,MAAO,EACX,CAMA,gBAAgBC,EAAc,CAC1B,KAAK,aAAeA,CACxB,CAMA,iBAAkB,CACd,OAAO,KAAK,YAChB,CAMA,SAAU,CACN,OAAO,KAAK,aAAe,KAAK,aAAa,QAAO,EAAK,EAC7D,CAMA,iBAAiB/B,EAAS,CAClB,KAAK,cACL,KAAK,aAAa,WAAWA,CAAO,CAE5C,CAMA,gBAAiB,CACb,OAAO,KAAK,aAAe,KAAK,aAAa,SAAQ,EAAK,IAC9D,CAMA,mBAAmB3C,EAAW,CAEtB,KAAK,iBACL,KAAK,sBAAwBA,EACzB,KAAK,sBAAwB,IAC7B,KAAK,eAAiB,GACtB,KAAK,qBAAuB,IAKhC,KAAK,aACL,KAAK,kBAAoBA,EACrB,KAAK,kBAAoB,IACzB,KAAK,WAAa,GAClB,KAAK,iBAAmB,GAGpC,CAOA,WAAW6C,EAAQ,CAEf,GAAI,KAAK,gBAAkB,KAAK,YAC5B,MAAO,CACH,YAAa,EACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,UAAW,KAAK,WAChC,EAIQ,MAAM8B,EAAe,KAAK,IAAI9B,EAAQ,KAAK,MAAM,EACjD,YAAK,QAAU8B,EAGf,KAAK,WAAa,GAClB,KAAK,iBAAmB,KAAK,oBAE7B,QAAQ,IAAI,mBAAmBA,CAAY,oBAAoB,KAAK,MAAM,IAAI,KAAK,SAAS,YAAY,KAAK,KAAK,EAAE,EAGhH,KAAK,QAAU,EACf,KAAK,YAAW,GAGhB,KAAK,eAAiB,GACtB,KAAK,qBAAuB,KAAK,yBAG9B,CACH,YAAaA,EACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,UAAW,KAAK,WAC5B,CACI,CAKA,aAAc,CACV,KAAK,OAAS,EACd,KAAK,QAEL,QAAQ,IAAI,0CAA0C,KAAK,KAAK,EAAE,EAE9D,KAAK,OAAS,GAEd,KAAK,YAAc,GACnB,KAAK,OAAS,GACd,QAAQ,IAAI,gCAAgC,GAG5C,KAAK,QAAO,CAEpB,CAKA,SAAU,CAEN,KAAK,OAAS,KAAK,UAGnB,MAAMC,EAAS,KAAK,YAAc,EAC5BC,EAAS,KAAK,aAAe,IACnC,KAAK,gBAAgBD,EAAQC,CAAM,EAGnC,KAAK,eAAiB,GACtB,KAAK,qBAAuB,KAAK,wBAA0B,EAG3D,KAAK,WAAa,GAClB,KAAK,iBAAmB,EAExB,QAAQ,IAAI,iDAAiD,KAAK,KAAK,EAAE,CAC7E,CAOA,KAAKC,EAAY,CACb,GAAI,KAAK,YAAa,MAAO,GAE7B,MAAMC,EAAa,KAAK,IAAID,EAAY,KAAK,UAAY,KAAK,MAAM,EACpE,YAAK,QAAUC,EAEf,QAAQ,IAAI,yBAAyBA,CAAU,aAAa,KAAK,MAAM,IAAI,KAAK,SAAS,EAAE,EACpFA,CACX,CAMA,SAASC,EAAY,CACjB,KAAK,OAASA,EACd,QAAQ,IAAI,qBAAqBA,CAAU,wBAAwB,KAAK,KAAK,EAAE,CACnF,CAMA,iBAAkB,CACd,MAAO,CACH,OAAQ,KAAK,OACb,UAAW,KAAK,UAChB,iBAAkB,KAAK,OAAS,KAAK,UACrC,MAAO,KAAK,MACZ,SAAU,KAAK,SACf,eAAgB,KAAK,eACrB,6BAA8B,KAAK,qBACnC,YAAa,KAAK,YAClB,WAAY,KAAK,UAC7B,CACI,CAKA,qBAAsB,CAClB,KAAK,OAAS,KAAK,UACnB,KAAK,MAAQ,KAAK,SAClB,KAAK,eAAiB,GACtB,KAAK,qBAAuB,EAC5B,KAAK,YAAc,GACnB,KAAK,WAAa,GAClB,KAAK,iBAAmB,EAExB,QAAQ,IAAI,8CAA8C,CAC9D,CASA,iBAAiBC,EAAQC,EAAQ3D,EAAQ,CAErCA,EAAS,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,CAAM,CAAC,EAGxC,MAAM4D,EAAOF,EAAO,QAAQ,IAAK,EAAE,EAC7BG,EAAOF,EAAO,QAAQ,IAAK,EAAE,EAE7BG,EAAK,SAASF,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCG,EAAK,SAASH,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCI,EAAK,SAASJ,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EAEnCK,EAAK,SAASJ,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCK,EAAK,SAASL,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCM,EAAK,SAASN,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EAGnC3D,EAAI,KAAK,MAAM4D,GAAMG,EAAKH,GAAM9D,CAAM,EACtC,EAAI,KAAK,MAAM+D,GAAMG,EAAKH,GAAM/D,CAAM,EACtCI,EAAI,KAAK,MAAM4D,GAAMG,EAAKH,GAAMhE,CAAM,EAGtCoE,EAASC,GAAM,CACjB,MAAMpE,EAAMoE,EAAE,SAAS,EAAE,EACzB,OAAOpE,EAAI,SAAW,EAAI,IAAMA,EAAMA,CAC1C,EAEA,MAAO,IAAImE,EAAMlE,CAAC,CAAC,GAAGkE,EAAM,CAAC,CAAC,GAAGA,EAAMhE,CAAC,CAAC,EAC7C,CACJ,CC3qBO,MAAMkE,CAAW,CACpB,YAAYC,EAAUC,EAASC,EAAc,GAAI,CAC7C,KAAK,SAAWF,EAChB,KAAK,QAAUC,EACf,KAAK,KAAO,CAAA,EACZ,KAAK,OAAS,CAAA,EAGd,QAASjF,EAAI,EAAGA,EAAIkF,EAAalF,IAC7B,KAAK,KAAK,KAAK,KAAK,SAAQ,CAAE,CAEtC,CAGA,KAAM,CACF,IAAImF,EACJ,OAAI,KAAK,KAAK,OAAS,EACnBA,EAAM,KAAK,KAAK,IAAG,EAEnBA,EAAM,KAAK,SAAQ,EAGvB,KAAK,OAAO,KAAKA,CAAG,EACbA,CACX,CAGA,QAAQA,EAAK,CACT,MAAMC,EAAQ,KAAK,OAAO,QAAQD,CAAG,EACjCC,IAAU,KACV,KAAK,OAAO,OAAOA,EAAO,CAAC,EAC3B,KAAK,QAAQD,CAAG,EAChB,KAAK,KAAK,KAAKA,CAAG,EAE1B,CAGA,YAAa,CACT,KAAO,KAAK,OAAO,OAAS,GAAG,CAC3B,MAAMA,EAAM,KAAK,OAAO,IAAG,EAC3B,KAAK,QAAQA,CAAG,EAChB,KAAK,KAAK,KAAKA,CAAG,CACtB,CACJ,CAGA,UAAW,CACP,MAAO,CACH,OAAQ,KAAK,KAAK,OAClB,OAAQ,KAAK,OAAO,OACpB,MAAO,KAAK,KAAK,OAAS,KAAK,OAAO,MAClD,CACI,CACJ,CCnDO,MAAME,CAAkB,CAC3B,aAAc,CACV,KAAK,QAAU,CAAA,EACf,KAAK,aAAe,CAAA,EACpB,KAAK,gBAAkB,CAAA,EACvB,KAAK,MAAQ,IAAI,GACrB,CAGA,IAAIC,EAAQ,CACR,KAAK,aAAa,KAAKA,CAAM,CACjC,CAGA,OAAOA,EAAQ,CACX,KAAK,gBAAgB,KAAKA,CAAM,CACpC,CAGA,WAAWxF,EAAMkF,EAAUC,EAASC,EAAc,GAAI,CAClD,KAAK,MAAM,IAAIpF,EAAM,IAAIiF,EAAWC,EAAUC,EAASC,CAAW,CAAC,CACvE,CAGA,YAAYpF,EAAM,CACd,MAAMyF,EAAO,KAAK,MAAM,IAAIzF,CAAI,EAChC,GAAIyF,EACA,OAAOA,EAAK,IAAG,EAEnB,MAAM,IAAI,MAAM,kBAAkBzF,CAAI,aAAa,CACvD,CAGA,aAAaA,EAAMwF,EAAQ,CACvB,MAAMC,EAAO,KAAK,MAAM,IAAIzF,CAAI,EAC5ByF,IACAA,EAAK,QAAQD,CAAM,EACnB,KAAK,OAAOA,CAAM,EAE1B,CAGA,OAAOpG,EAAW,CAEd,KAAK,iBAAgB,EACrB,KAAK,gBAAe,EAGpB,QAAS,EAAI,KAAK,QAAQ,OAAS,EAAG,GAAK,EAAG,IAAK,CAC/C,MAAMoG,EAAS,KAAK,QAAQ,CAAC,EAE7B,GAAIA,EAAO,UAAW,CAClB,KAAK,gBAAgB,KAAKA,CAAM,EAChC,QACJ,CAEIA,EAAO,QACPA,EAAO,OAAOpG,CAAS,CAE/B,CACJ,CAGA,OAAOL,EAAKM,EAAgB,EAAG,CAC3B,UAAWmG,KAAU,KAAK,QAClBA,EAAO,SAAW,CAACA,EAAO,WAC1BA,EAAO,OAAOzG,EAAKM,CAAa,CAG5C,CAGA,kBAAmB,CACX,KAAK,aAAa,OAAS,IAC3B,KAAK,QAAQ,KAAK,GAAG,KAAK,YAAY,EACtC,KAAK,aAAa,OAAS,EAEnC,CAGA,iBAAkB,CACd,GAAI,KAAK,gBAAgB,OAAS,EAAG,CACjC,UAAWqG,KAAkB,KAAK,gBAAiB,CAC/C,MAAMJ,EAAQ,KAAK,QAAQ,QAAQI,CAAc,EAC7CJ,IAAU,IACV,KAAK,QAAQ,OAAOA,EAAO,CAAC,CAEpC,CACA,KAAK,gBAAgB,OAAS,CAClC,CACJ,CAGA,UAAU/F,EAAK,CACX,OAAO,KAAK,QAAQ,OAAO8F,GAAOA,EAAI,OAAO9F,CAAG,CAAC,CACrD,CAGA,SAASoG,EAAI,CACT,OAAO,KAAK,QAAQ,KAAKN,GAAOA,EAAI,KAAOM,CAAE,CACjD,CAGA,WAAY,CACR,OAAO,KAAK,QAAQ,OAAON,GAAOA,EAAI,QAAU,CAACA,EAAI,SAAS,CAClE,CAGA,YAAa,CACT,OAAO,KAAK,QAAQ,OAAOA,GAAOA,EAAI,SAAW,CAACA,EAAI,SAAS,CACnE,CAGA,gBAAgBO,EAAWC,EAAWC,EAAU,CAC5C,MAAMC,EAAS,KAAK,UAAUH,CAAS,EACjCI,EAAS,KAAK,UAAUH,CAAS,EAEvC,UAAWI,KAAQF,EACf,GAAKE,EAAK,OAEV,UAAWC,KAAQF,EACVE,EAAK,QAEND,EAAK,aAAaC,CAAI,GACtBJ,EAASG,EAAMC,CAAI,CAInC,CAGA,yBAAyBN,EAAWC,EAAWC,EAAUK,EAAW,GAAI,CACpE,MAAMJ,EAAS,KAAK,UAAUH,CAAS,EACjCI,EAAS,KAAK,UAAUH,CAAS,EAGjCO,EAAc,IAAI,IAGxB,UAAWf,KAAOW,EAAQ,CACtB,GAAI,CAACX,EAAI,OAAQ,SAEjB,MAAMgB,EAAQ,KAAK,MAAMhB,EAAI,SAAS,EAAIc,CAAQ,EAC5CG,EAAQ,KAAK,MAAMjB,EAAI,SAAS,EAAIc,CAAQ,EAC5CjI,EAAM,GAAGmI,CAAK,IAAIC,CAAK,GAExBF,EAAY,IAAIlI,CAAG,GACpBkI,EAAY,IAAIlI,EAAK,EAAE,EAE3BkI,EAAY,IAAIlI,CAAG,EAAE,KAAKmH,CAAG,CACjC,CAGA,UAAWY,KAAQF,EAAQ,CACvB,GAAI,CAACE,EAAK,OAAQ,SAElB,MAAMI,EAAQ,KAAK,MAAMJ,EAAK,SAAS,EAAIE,CAAQ,EAC7CG,EAAQ,KAAK,MAAML,EAAK,SAAS,EAAIE,CAAQ,EAGnD,QAASI,EAAK,GAAIA,GAAM,EAAGA,IACvB,QAASC,EAAK,GAAIA,GAAM,EAAGA,IAAM,CAC7B,MAAMtI,EAAM,GAAGmI,EAAQE,CAAE,IAAID,EAAQE,CAAE,GACjCC,EAAgBL,EAAY,IAAIlI,CAAG,EAEzC,GAAIuI,EACA,UAAWP,KAAQO,EACXR,EAAK,aAAaC,CAAI,GACtBJ,EAASG,EAAMC,CAAI,CAInC,CAER,CACJ,CAGA,OAAQ,CACJ,KAAK,QAAQ,OAAS,EACtB,KAAK,aAAa,OAAS,EAC3B,KAAK,gBAAgB,OAAS,EAG9B,UAAWT,KAAQ,KAAK,MAAM,OAAM,EAChCA,EAAK,WAAU,CAEvB,CAGA,UAAW,CACP,MAAMiB,EAAY,CAAA,EAClB,SAAW,CAAC1G,EAAMyF,CAAI,IAAK,KAAK,MAAM,UAClCiB,EAAU1G,CAAI,EAAIyF,EAAK,SAAQ,EAGnC,MAAO,CACH,aAAc,KAAK,QAAQ,OAC3B,cAAe,KAAK,UAAS,EAAG,OAChC,eAAgB,KAAK,WAAU,EAAG,OAClC,iBAAkB,KAAK,aAAa,OACpC,gBAAiB,KAAK,gBAAgB,OACtC,MAAOiB,CACnB,CACI,CACJ,CC3MO,MAAMC,CAAW,CACpB,YAAY5I,EAAQ6I,EAAW,CAC3B,KAAK,OAAS7I,EACd,KAAK,IAAMA,EAAO,WAAW,IAAI,EACjC,KAAK,UAAY6I,EAGjB,KAAK,UAAY,GACjB,KAAK,SAAW,GAGhB,KAAK,UAAY,GACjB,KAAK,cAAgB,IAAO,KAAK,UACjC,KAAK,aAAe,IAGpB,KAAK,cAAgB,EACrB,KAAK,YAAc,EACnB,KAAK,YAAc,EAGnB,KAAK,WAAa,EAClB,KAAK,SAAW,EAChB,KAAK,WAAa,EAGlB,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,CAC3C,CAEA,MAAM,MAAO,CACT,eAAQ,IAAI,uCAAuC,EAGnD,KAAK,YAAW,EAGhB,KAAK,kBAAiB,EAGtB,KAAK,MAAK,EAEH,QAAQ,QAAO,CAC1B,CAEA,aAAc,CAEV,KAAK,IAAI,sBAAwB,GAGjC,KAAK,OAAO,MAAQ,IACpB,KAAK,OAAO,OAAS,IAErB,QAAQ,IAAI,uBAAuB,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,EAAE,CAChF,CAEA,mBAAoB,CAEhB,KAAK,aAAe,IAAI9I,EAAa,KAAK,MAAM,EAGhD,KAAK,kBAAoB,IAAIyH,EAG7B,MAAMvB,EAAS,KAAK,OAAO,MAAQ,EAC7BC,EAAS,KAAK,OAAO,OAAS,IACpC,KAAK,WAAa,IAAI9B,EAAW6B,EAAQC,EAAQ,KAAK,OAAO,MAAO,KAAK,OAAO,OAAQ,KAAK,iBAAiB,EAI9G,QAAQ,IAAI,0BAA0B,CAC1C,CAEA,OAAQ,CACC,KAAK,YACN,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,YAAc,YAAY,IAAG,EAClC,KAAK,cAAgB,KAAK,YAC1B,KAAK,YAAc,EACnB,KAAK,WAAa,EAClB,KAAK,SAAW,EAChB,sBAAsB,KAAK,QAAQ,EACnC,QAAQ,IAAI,uCAAuC,EAE3D,CAEA,OAAQ,CACJ,KAAK,SAAW,GAChB,QAAQ,IAAI,aAAa,CAC7B,CAEA,QAAS,CACD,KAAK,WACL,KAAK,SAAW,GAEhB,KAAK,YAAc,YAAY,IAAG,EAClC,KAAK,cAAgB,KAAK,YAC1B,KAAK,YAAc,EACnB,QAAQ,IAAI,cAAc,EAElC,CAEA,SAAU,CACN,KAAK,UAAY,GAGb,KAAK,cACL,KAAK,aAAa,QAAO,EAG7B,QAAQ,IAAI,uBAAuB,CACvC,CAEA,SAAS4C,EAAa,CAClB,GAAI,CAAC,KAAK,UAAW,OAGrB,IAAIC,EAAYD,EAAc,KAAK,cAOnC,GANIC,EAAY,KAAK,eACjBA,EAAY,KAAK,cAGrB,KAAK,cAAgBD,EAEjB,CAAC,KAAK,SAAU,CAKhB,IAHA,KAAK,aAAeC,EAGb,KAAK,aAAe,KAAK,eAC5B,KAAK,OAAO,KAAK,aAAa,EAC9B,KAAK,aAAe,KAAK,cAI7B,MAAMzH,EAAgB,KAAK,YAAc,KAAK,cAG9C,KAAK,OAAOA,CAAa,EAGzB,KAAK,iBAAiByH,CAAS,CACnC,CAEA,sBAAsB,KAAK,QAAQ,CACvC,CAEA,OAAO1H,EAAW,CAOd,GALI,KAAK,cACL,KAAK,aAAa,OAAM,EAIxB,KAAK,YAAc,KAAK,aAAc,CACtC,MAAMkD,EAAgB,KAAK,aAAa,kBAAiB,EACzD,KAAK,WAAW,OAAOlD,EAAWkD,CAAa,EAG3C,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,KAAI,EAIpB,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,WAAW,EAAE,EAI7B,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,KAAK,EAAE,EAIvB,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,SAAS,CAAC,CAElC,CAGI,KAAK,mBACL,KAAK,kBAAkB,OAAOlD,CAAS,EAI3C,KAAK,mBAAkB,CAK3B,CAKA,oBAAqB,CACjB,GAAI,CAAC,KAAK,kBAAmB,OAE7B,MAAMQ,EAAS,CACX,KAAM,IACN,MAAO,KAAK,OAAO,MAAQ,GAC3B,IAAK,IACL,OAAQ,KAAK,OAAO,OAAS,EACzC,EAEcmH,EAAc,KAAK,kBAAkB,UAAU,YAAY,EACjE,UAAW7F,KAAc6F,EACjB7F,EAAW,cAActB,CAAM,GAC/B,KAAK,kBAAkB,aAAa,aAAcsB,CAAU,CAGxE,CAEA,OAAO7B,EAAgB,EAAG,CAEtB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,EAAG,EAAG,KAAK,OAAO,MAAO,KAAK,OAAO,MAAM,EAG7D,KAAK,gBAAe,EAGhB,KAAK,YACL,KAAK,WAAW,OAAO,KAAK,IAAKA,CAAa,EAI9C,KAAK,mBACL,KAAK,kBAAkB,OAAO,KAAK,IAAKA,CAAa,EAIzD,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,SACrB,KAAK,IAAI,SAAS,YAAa,KAAK,OAAO,MAAQ,EAAG,EAAE,EAGpD,KAAK,eACL,KAAK,iBAAgB,EAGrB,KAAK,aAAa,OAAO,KAAK,GAAG,GAIrC,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,OACrB,KAAK,IAAI,SAAS,QAAQ,KAAK,UAAU,GAAI,GAAI,EAAE,EAGnD,KAAK,uBAAsB,CAC/B,CAEA,kBAAmB,CACf,GAAI,CAAC,KAAK,aAAc,OAGxB,MAAMZ,EAAW,KAAK,aAAa,kBAAiB,EACpD,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,OACrB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,aAAaA,EAAS,EAAE,QAAQ,CAAC,CAAC,KAAKA,EAAS,EAAE,QAAQ,CAAC,CAAC,GAAI,GAAI,EAAE,EAGxF,MAAMuI,EAAU,CAAC,OAAQ,QAAS,UAAU,EAC5C,IAAIC,EAAU,GAEd,UAAW1I,KAAUyI,EACb,KAAK,aAAa,aAAazI,CAAM,GACrC,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,GAAGA,EAAO,YAAW,CAAE,WAAY,GAAI0I,CAAO,IAEhE,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,GAAG1I,CAAM,aAAc,GAAI0I,CAAO,GAExDA,GAAW,GAaf,GATA,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,UAAU,KAAK,aAAa,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,aAAa,cAAc,EAAE,QAAQ,CAAC,CAAC,GAAI,GAAIA,EAAU,EAAE,EAGzI,KAAK,aAAa,eAClB,KAAK,IAAI,SAAS,iBAAiB,KAAK,aAAa,QAAQ,IAAI,WAAY,GAAIA,EAAU,EAAE,EAI7F,KAAK,kBAAmB,CACxB,MAAMC,EAAQ,KAAK,kBAAkB,SAAQ,EAC7C,KAAK,IAAI,SAAS,YAAYA,EAAM,YAAY,KAAKA,EAAM,aAAa,WAAY,GAAID,EAAU,EAAE,EAEpG,MAAMF,EAAc,KAAK,kBAAkB,UAAU,YAAY,EACjE,KAAK,IAAI,SAAS,gBAAgBA,EAAY,MAAM,GAAI,GAAIE,EAAU,EAAE,CAC5E,CACJ,CAEA,iBAAiBH,EAAW,CACxB,KAAK,aACL,KAAK,UAAYA,EAGb,KAAK,UAAY,MACjB,KAAK,WAAa,KAAK,MAAO,KAAK,WAAa,IAAQ,KAAK,QAAQ,EACrE,KAAK,WAAa,EAClB,KAAK,SAAW,EAExB,CAEA,iBAAkB,CAEd,KAAK,IAAI,UAAY,UACrB,QAAS5G,EAAI,EAAGA,EAAI,IAAKA,IAAK,CAC1B,MAAMhD,EAAKgD,EAAI,GAAM,KAAK,OAAO,MAC3B/C,EAAK+C,EAAI,GAAM,KAAK,OAAO,OAC3BiH,EAAQjH,EAAI,EAAK,EACvB,KAAK,IAAI,SAAShD,EAAGC,EAAGgK,EAAMA,CAAI,CACtC,CACJ,CAKA,wBAAyB,CACrB,GAAI,CAAC,KAAK,WAAY,OAEtB,MAAMC,EAAe,KAAK,WAAW,gBAAe,EAG9CC,EAAa,KAAK,OAAO,MAAQ,IACjCC,EAAa,GACbC,EAAiB,IACjBC,EAAkB,GAGxB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAASH,EAAYC,EAAYC,EAAgBC,CAAe,EAGzE,KAAK,IAAI,YAAc,UACvB,KAAK,IAAI,UAAY,EACrB,KAAK,IAAI,WAAWH,EAAYC,EAAYC,EAAgBC,CAAe,EAG3E,MAAMC,EAAcF,EAAiBH,EAAa,iBAClD,IAAIM,EAAc,UAEdN,EAAa,iBAAmB,GAChCM,EAAc,UACPN,EAAa,iBAAmB,KACvCM,EAAc,WAGlB,KAAK,IAAI,UAAYA,EACrB,KAAK,IAAI,SAASL,EAAa,EAAGC,EAAa,EAAGG,EAAc,EAAGD,EAAkB,CAAC,EAGtF,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,SACrB,KAAK,IAAI,SACL,GAAGJ,EAAa,MAAM,IAAIA,EAAa,SAAS,GAChDC,EAAaE,EAAiB,EAC9BD,EAAaE,EAAkB,EAAI,CAC/C,EAGQ,MAAMG,EAASL,EAAaE,EAAkB,GAC9C,KAAK,IAAI,UAAY,QACrB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,UAAUJ,EAAa,KAAK,GAAI,KAAK,OAAO,MAAQ,GAAIO,CAAM,EAGhF,MAAMC,EAAe,GACfC,EAAkB,GAClBC,EAAiB,KAAK,OAAO,MAAQ,GAAMV,EAAa,MAAQS,EAEtE,QAAS3H,EAAI,EAAGA,EAAIkH,EAAa,MAAOlH,IAAK,CACzC,MAAM6H,EAAQD,EAAkB5H,EAAI2H,EAC9BG,EAAQL,EAAS,GAGvB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,UAAS,EAClB,KAAK,IAAI,OAAOI,EAAOC,EAAQJ,EAAe,CAAC,EAC/C,KAAK,IAAI,OAAOG,EAAQH,EAAe,EAAGI,EAAQJ,EAAe,CAAC,EAClE,KAAK,IAAI,OAAOG,EAAQH,EAAe,EAAGI,EAAQJ,EAAe,CAAC,EAClE,KAAK,IAAI,UAAS,EAClB,KAAK,IAAI,KAAI,CACjB,CAGA,GAAIR,EAAa,eAAgB,CAC7B,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,QACrB,MAAMa,GAAcb,EAAa,6BAA+B,KAAM,QAAQ,CAAC,EAC/E,KAAK,IAAI,SAAS,iBAAiBa,CAAU,IAAK,KAAK,OAAO,MAAQ,GAAIN,EAAS,EAAE,CACzF,CAGIP,EAAa,cACb,KAAK,IAAI,UAAY,qBACrB,KAAK,IAAI,SAAS,EAAG,EAAG,KAAK,OAAO,MAAO,KAAK,OAAO,MAAM,EAE7D,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,SACrB,KAAK,IAAI,SAAS,YAAa,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,CAAC,EAE5E,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,qBAAsB,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,EAAI,EAAE,EAElG,CAEA,cAAe,CAEX,QAAQ,IAAI,gBAAgB,CAChC,CACJ,CC3aA,SAAS,iBAAiB,mBAAoB,IAAM,CAChD,MAAMrJ,EAAS,SAAS,eAAe,YAAY,EAC7CmK,EAAK,SAAS,eAAe,IAAI,EAEvC,GAAI,CAACnK,EAAQ,CACT,QAAQ,MAAM,uBAAuB,EACrC,MACJ,CAGA,MAAMoK,EAAO,IAAIxB,EAAW5I,EAAQmK,CAAE,EAGtCC,EAAK,OAAO,KAAK,IAAM,CACnB,QAAQ,IAAI,yCAAyC,EACrDD,EAAG,UAAY,sCACnB,CAAC,EAAE,MAAME,GAAS,CACd,QAAQ,MAAM,6BAA8BA,CAAK,EACjDF,EAAG,UAAY,uCACnB,CAAC,EAGD,OAAO,iBAAiB,SAAU,IAAM,CACpCC,EAAK,aAAY,CACrB,CAAC,EAGD,SAAS,iBAAiB,mBAAoB,IAAM,CAC5C,SAAS,OACTA,EAAK,MAAK,EAEVA,EAAK,OAAM,CAEnB,CAAC,CACL,CAAC"}