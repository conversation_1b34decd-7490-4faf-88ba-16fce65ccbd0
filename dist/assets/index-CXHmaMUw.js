var F=Object.defineProperty;var T=(r,t,i)=>t in r?F(r,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[t]=i;var P=(r,t,i)=>T(r,typeof t!="symbol"?t+"":t,i);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))e(s);new MutationObserver(s=>{for(const h of s)if(h.type==="childList")for(const n of h.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&e(n)}).observe(document,{childList:!0,subtree:!0});function i(s){const h={};return s.integrity&&(h.integrity=s.integrity),s.referrerPolicy&&(h.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?h.credentials="include":s.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function e(s){if(s.ep)return;s.ep=!0;const h=i(s);fetch(s.href,h)}})();class o{constructor(t=0,i=0){this.x=t,this.y=i}static zero(){return new o(0,0)}static one(){return new o(1,1)}static up(){return new o(0,-1)}static down(){return new o(0,1)}static left(){return new o(-1,0)}static right(){return new o(1,0)}add(t){return new o(this.x+t.x,this.y+t.y)}subtract(t){return new o(this.x-t.x,this.y-t.y)}multiply(t){return new o(this.x*t,this.y*t)}divide(t){if(t===0)throw new Error("Division by zero");return new o(this.x/t,this.y/t)}magnitude(){return Math.sqrt(this.x*this.x+this.y*this.y)}normalize(){const t=this.magnitude();return t===0?o.zero():this.divide(t)}distance(t){return this.subtract(t).magnitude()}dot(t){return this.x*t.x+this.y*t.y}addInPlace(t){return this.x+=t.x,this.y+=t.y,this}subtractInPlace(t){return this.x-=t.x,this.y-=t.y,this}multiplyInPlace(t){return this.x*=t,this.y*=t,this}normalizeInPlace(){const t=this.magnitude();return t>0&&(this.x/=t,this.y/=t),this}set(t,i){return this.x=t,this.y=i,this}setFromVector(t){return this.x=t.x,this.y=t.y,this}angle(){return Math.atan2(this.y,this.x)}static fromAngle(t,i=1){return new o(Math.cos(t)*i,Math.sin(t)*i)}rotate(t){const i=Math.cos(t),e=Math.sin(t),s=this.x*i-this.y*e,h=this.x*e+this.y*i;return new o(s,h)}rotateInPlace(t){const i=Math.cos(t),e=Math.sin(t),s=this.x*i-this.y*e,h=this.x*e+this.y*i;return this.x=s,this.y=h,this}perpendicular(){return new o(-this.y,this.x)}clone(){return new o(this.x,this.y)}equals(t,i=0){return i===0?this.x===t.x&&this.y===t.y:Math.abs(this.x-t.x)<=i&&Math.abs(this.y-t.y)<=i}toString(){return`Vector2(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`}}class S{constructor(t){this.canvas=t,this.keys=new Map,this.keysPressed=new Map,this.keysReleased=new Map,this.mousePosition=new o(0,0),this.mouseButtons=new Map,this.mousePressed=new Map,this.mouseReleased=new Map,this.touches=new Map,this.touchStarted=new Map,this.touchEnded=new Map,this.keyMappings=new Map,this.setupDefaultMappings(),this.isTouchDevice="ontouchstart"in window,this.virtualJoystick=null,this.handleKeyDown=this.handleKeyDown.bind(this),this.handleKeyUp=this.handleKeyUp.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleTouchStart=this.handleTouchStart.bind(this),this.handleTouchEnd=this.handleTouchEnd.bind(this),this.handleTouchMove=this.handleTouchMove.bind(this),this.init()}init(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("keyup",this.handleKeyUp),this.canvas.addEventListener("mousedown",this.handleMouseDown),this.canvas.addEventListener("mouseup",this.handleMouseUp),this.canvas.addEventListener("mousemove",this.handleMouseMove),this.canvas.addEventListener("touchstart",this.handleTouchStart,{passive:!1}),this.canvas.addEventListener("touchend",this.handleTouchEnd,{passive:!1}),this.canvas.addEventListener("touchmove",this.handleTouchMove,{passive:!1}),this.canvas.addEventListener("contextmenu",t=>t.preventDefault()),this.isTouchDevice&&this.initVirtualJoystick(),console.log("InputManager initialized")}setupDefaultMappings(){this.keyMappings.set("moveUp",["ArrowUp","KeyW"]),this.keyMappings.set("moveDown",["ArrowDown","KeyS"]),this.keyMappings.set("moveLeft",["ArrowLeft","KeyA"]),this.keyMappings.set("moveRight",["ArrowRight","KeyD"]),this.keyMappings.set("fire",["Space","Enter"]),this.keyMappings.set("pause",["Escape","KeyP"]),this.keyMappings.set("interact",["KeyE","KeyF"]),this.keyMappings.set("debug",["F1"])}handleKeyDown(t){const i=t.code;this.keys.get(i)||this.keysPressed.set(i,!0),this.keys.set(i,!0),this.isGameKey(i)&&t.preventDefault()}handleKeyUp(t){const i=t.code;this.keys.set(i,!1),this.keysReleased.set(i,!0),this.isGameKey(i)&&t.preventDefault()}handleMouseDown(t){const i=t.button;this.mouseButtons.get(i)||this.mousePressed.set(i,!0),this.mouseButtons.set(i,!0),this.updateMousePosition(t),t.preventDefault()}handleMouseUp(t){const i=t.button;this.mouseButtons.set(i,!1),this.mouseReleased.set(i,!0),this.updateMousePosition(t),t.preventDefault()}handleMouseMove(t){this.updateMousePosition(t)}updateMousePosition(t){const i=this.canvas.getBoundingClientRect();this.mousePosition.set(t.clientX-i.left,t.clientY-i.top)}handleTouchStart(t){t.preventDefault();for(const i of t.changedTouches){const e=this.getTouchPosition(i);this.touches.set(i.identifier,e),this.touchStarted.set(i.identifier,e.clone()),this.virtualJoystick&&this.virtualJoystick.handleTouchStart(i.identifier,e)}}handleTouchEnd(t){t.preventDefault();for(const i of t.changedTouches){const e=this.getTouchPosition(i);this.touchEnded.set(i.identifier,e),this.touches.delete(i.identifier),this.virtualJoystick&&this.virtualJoystick.handleTouchEnd(i.identifier)}}handleTouchMove(t){t.preventDefault();for(const i of t.changedTouches){const e=this.getTouchPosition(i);this.touches.set(i.identifier,e),this.virtualJoystick&&this.virtualJoystick.handleTouchMove(i.identifier,e)}}getTouchPosition(t){const i=this.canvas.getBoundingClientRect();return new o(t.clientX-i.left,t.clientY-i.top)}isKeyDown(t){return this.keys.get(t)||!1}isKeyPressed(t){return this.keysPressed.get(t)||!1}isKeyReleased(t){return this.keysReleased.get(t)||!1}isMouseDown(t=0){return this.mouseButtons.get(t)||!1}isMousePressed(t=0){return this.mousePressed.get(t)||!1}isMouseReleased(t=0){return this.mouseReleased.get(t)||!1}isActionDown(t){const i=this.keyMappings.get(t);return i?i.some(e=>this.isKeyDown(e)):!1}isActionPressed(t){const i=this.keyMappings.get(t);return i?i.some(e=>this.isKeyPressed(e)):!1}isActionReleased(t){const i=this.keyMappings.get(t);return i?i.some(e=>this.isKeyReleased(e)):!1}getMovementVector(){const t=new o(0,0);if(this.isActionDown("moveLeft")&&(t.x-=1),this.isActionDown("moveRight")&&(t.x+=1),this.isActionDown("moveUp")&&(t.y-=1),this.isActionDown("moveDown")&&(t.y+=1),this.virtualJoystick&&this.virtualJoystick.isActive()){const i=this.virtualJoystick.getInput();t.addInPlace(i)}return t.magnitude()>1&&t.normalizeInPlace(),t}setKeyMapping(t,i){this.keyMappings.set(t,Array.isArray(i)?i:[i])}addKeyMapping(t,i){const e=this.keyMappings.get(t)||[];e.push(i),this.keyMappings.set(t,e)}removeKeyMapping(t,i){const s=(this.keyMappings.get(t)||[]).filter(h=>h!==i);this.keyMappings.set(t,s)}isGameKey(t){for(const i of this.keyMappings.values())if(i.includes(t))return!0;return!1}initVirtualJoystick(){this.virtualJoystick=new k(this.canvas)}update(){this.keysPressed.clear(),this.keysReleased.clear(),this.mousePressed.clear(),this.mouseReleased.clear(),this.touchStarted.clear(),this.touchEnded.clear(),this.virtualJoystick&&this.virtualJoystick.update()}render(t){this.virtualJoystick&&this.virtualJoystick.render(t)}destroy(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("keyup",this.handleKeyUp),this.canvas.removeEventListener("mousedown",this.handleMouseDown),this.canvas.removeEventListener("mouseup",this.handleMouseUp),this.canvas.removeEventListener("mousemove",this.handleMouseMove),this.canvas.removeEventListener("touchstart",this.handleTouchStart),this.canvas.removeEventListener("touchend",this.handleTouchEnd),this.canvas.removeEventListener("touchmove",this.handleTouchMove),this.virtualJoystick&&this.virtualJoystick.destroy(),console.log("InputManager destroyed")}}class k{constructor(t){this.canvas=t,this.active=!1,this.touchId=null,this.center=new o(100,t.height-100),this.knobPosition=new o(100,t.height-100),this.maxDistance=50,this.deadZone=.1,this.baseRadius=60,this.knobRadius=25,this.baseColor="rgba(255, 255, 255, 0.3)",this.knobColor="rgba(255, 255, 255, 0.7)"}handleTouchStart(t,i){i.distance(this.center)<=this.baseRadius&&(this.active=!0,this.touchId=t,this.knobPosition.setFromVector(i),this.clampKnobPosition())}handleTouchMove(t,i){this.active&&this.touchId===t&&(this.knobPosition.setFromVector(i),this.clampKnobPosition())}handleTouchEnd(t){this.active&&this.touchId===t&&(this.active=!1,this.touchId=null,this.knobPosition.setFromVector(this.center))}clampKnobPosition(){const t=this.knobPosition.subtract(this.center);t.magnitude()>this.maxDistance&&(t.normalizeInPlace().multiplyInPlace(this.maxDistance),this.knobPosition=this.center.add(t))}getInput(){if(!this.active)return new o(0,0);const t=this.knobPosition.subtract(this.center),i=t.magnitude()/this.maxDistance;return i<this.deadZone?new o(0,0):t.normalize().multiply(i)}isActive(){return this.active}update(){this.center.set(100,this.canvas.height-100),this.active||this.knobPosition.setFromVector(this.center)}render(t){t.save(),t.fillStyle=this.baseColor,t.strokeStyle="rgba(255, 255, 255, 0.5)",t.lineWidth=2,t.beginPath(),t.arc(this.center.x,this.center.y,this.baseRadius,0,Math.PI*2),t.fill(),t.stroke(),t.fillStyle=this.knobColor,t.beginPath(),t.arc(this.knobPosition.x,this.knobPosition.y,this.knobRadius,0,Math.PI*2),t.fill(),t.stroke(),t.restore()}destroy(){}}const y=class y{constructor(t=0,i=0){this.position=new o(t,i),this.velocity=new o(0,0),this.acceleration=new o(0,0),this.rotation=0,this.scale=new o(1,1),this.active=!0,this.visible=!0,this.destroyed=!1,this.collisionRadius=0,this.collisionBounds={x:0,y:0,width:0,height:0},this.id=y.generateId(),this.tags=new Set}static generateId(){return++y.idCounter}update(t){this.active&&(this.velocity.addInPlace(this.acceleration.multiply(t/1e3)),this.position.addInPlace(this.velocity.multiply(t/1e3)),this.updateCollisionBounds())}render(t,i=0){if(!this.visible)return;const e=this.position.add(this.velocity.multiply(i/1e3));t.save(),t.translate(e.x,e.y),t.rotate(this.rotation),t.scale(this.scale.x,this.scale.y),this.collisionRadius>0&&(t.strokeStyle="#ff0000",t.beginPath(),t.arc(0,0,this.collisionRadius,0,Math.PI*2),t.stroke()),t.restore()}updateCollisionBounds(){this.collisionBounds.x=this.position.x-this.collisionRadius,this.collisionBounds.y=this.position.y-this.collisionRadius,this.collisionBounds.width=this.collisionRadius*2,this.collisionBounds.height=this.collisionRadius*2}collidesWith(t){return!this.active||!t.active?!1:this.collisionRadius>0&&t.collisionRadius>0?this.position.distance(t.position)<this.collisionRadius+t.collisionRadius:!1}destroy(){this.destroyed=!0,this.active=!1,this.visible=!1}reset(){this.position.set(0,0),this.velocity.set(0,0),this.acceleration.set(0,0),this.rotation=0,this.scale.set(1,1),this.active=!0,this.visible=!0,this.destroyed=!1,this.tags.clear()}addTag(t){this.tags.add(t)}removeTag(t){this.tags.delete(t)}hasTag(t){return this.tags.has(t)}distanceTo(t){return this.position.distance(t.position)}directionTo(t){return t.position.subtract(this.position).normalize()}lookAt(t){const i=this.directionTo(t);this.rotation=i.angle()}moveTowards(t,i,e){const h=this.directionTo(t).multiply(i*e/1e3);this.position.addInPlace(h)}applyForce(t){this.acceleration.addInPlace(t)}isOutOfBounds(t){return this.position.x<t.left||this.position.x>t.right||this.position.y<t.top||this.position.y>t.bottom}wrapAroundBounds(t){this.position.x<t.left&&(this.position.x=t.right),this.position.x>t.right&&(this.position.x=t.left),this.position.y<t.top&&(this.position.y=t.bottom),this.position.y>t.bottom&&(this.position.y=t.top)}clampToBounds(t){this.position.x=Math.max(t.left,Math.min(t.right,this.position.x)),this.position.y=Math.max(t.top,Math.min(t.bottom,this.position.y))}};P(y,"idCounter",0);let v=y;class w extends v{constructor(t=0,i=0){super(t,i),this.speed=600,this.damage=1,this.lifetime=3e3,this.age=0,this.width=4,this.height=12,this.collisionRadius=3,this.color="#FFD700",this.trailColor="#FFA500",this.trailPositions=[],this.maxTrailLength=8,this.trailFadeRate=.8,this.type="player",this.owner=null,this.addTag("projectile"),this.active=!1,this.visible=!1}initialize(t,i,e=600,s="player",h=null){return this.position.setFromVector(t),this.velocity=i.normalize().multiply(e),this.speed=e,this.type=s,this.owner=h,this.age=0,this.trailPositions=[],this.setupVisualsByType(),this.active=!0,this.visible=!0,this.destroyed=!1,this.tags.clear(),this.addTag("projectile"),this.addTag(s+"Projectile"),this}setupVisualsByType(){switch(this.type){case"player":this.color="#FFD700",this.trailColor="#FFA500",this.width=4,this.height=12;break;case"enemy":this.color="#FF4444",this.trailColor="#FF8888",this.width=3,this.height=8;break;default:this.color="#FFFFFF",this.trailColor="#CCCCCC";break}}update(t){if(this.active){if(this.age+=t,this.age>=this.lifetime){this.destroy();return}this.updateTrail(),super.update(t),this.updateCollisionBounds()}}updateTrail(){this.trailPositions.unshift(this.position.clone()),this.trailPositions.length>this.maxTrailLength&&this.trailPositions.pop()}render(t,i=0){if(!this.visible)return;const e=this.position.add(this.velocity.multiply(i/1e3));t.save(),this.renderTrail(t,i),this.renderProjectile(t,e),window.DEBUG_MODE&&this.renderDebugInfo(t,e),t.restore()}renderTrail(t,i){if(!(this.trailPositions.length<2)){t.strokeStyle=this.trailColor,t.lineWidth=2,t.lineCap="round";for(let e=0;e<this.trailPositions.length-1;e++){const s=Math.pow(this.trailFadeRate,e),h=this.trailPositions[e],n=this.trailPositions[e+1];let a=h;if(e===0){const l=this.velocity.multiply(i/1e3);a=h.add(l)}t.globalAlpha=s,t.beginPath(),t.moveTo(a.x,a.y),t.lineTo(n.x,n.y),t.stroke()}t.globalAlpha=1}}renderProjectile(t,i){t.translate(i.x,i.y),t.fillStyle=this.color,t.fillRect(-this.width/2,-this.height/2,this.width,this.height),t.fillStyle="#FFFFFF",t.fillRect(-1,-this.height/2,2,this.height)}drawPlayerProjectile(t){t.beginPath(),t.moveTo(0,-this.height/2),t.lineTo(this.width/2,0),t.lineTo(0,this.height/2),t.lineTo(-this.width/2,0),t.closePath(),t.fill(),t.stroke(),t.fillStyle=this.lightenColor(this.color,.3),t.beginPath(),t.moveTo(0,-this.height/4),t.lineTo(this.width/4,0),t.lineTo(0,this.height/4),t.lineTo(-this.width/4,0),t.closePath(),t.fill()}drawEnemyProjectile(t){t.beginPath(),t.ellipse(0,0,this.width/2,this.height/2,0,0,Math.PI*2),t.fill(),t.stroke(),t.fillStyle=this.lightenColor(this.color,.2),t.beginPath(),t.ellipse(0,-this.height/6,this.width/4,this.height/4,0,0,Math.PI*2),t.fill()}renderDebugInfo(t,i){t.resetTransform(),t.strokeStyle="#FF0000",t.lineWidth=1,t.beginPath(),t.arc(i.x,i.y,this.collisionRadius,0,Math.PI*2),t.stroke(),t.strokeStyle="#00FF00",t.lineWidth=1,t.beginPath(),t.moveTo(i.x,i.y);const e=i.add(this.velocity.multiply(.05));t.lineTo(e.x,e.y),t.stroke(),t.fillStyle="#FFFF00",t.font="10px Arial",t.fillText(`${Math.floor(this.age)}ms`,i.x+10,i.y-10)}isOutOfBounds(t){const i=Math.max(this.width,this.height);return this.position.x<t.left-i||this.position.x>t.right+i||this.position.y<t.top-i||this.position.y>t.bottom+i}onCollision(t){this.destroy()}reset(){super.reset(),this.age=0,this.trailPositions=[],this.type="player",this.owner=null,this.speed=600,this.damage=1,this.lifetime=3e3}darkenColor(t,i){const e=t.replace("#",""),s=Math.floor(parseInt(e.substr(0,2),16)*(1-i)),h=Math.floor(parseInt(e.substr(2,2),16)*(1-i)),n=Math.floor(parseInt(e.substr(4,2),16)*(1-i));return`rgb(${s}, ${h}, ${n})`}lightenColor(t,i){const e=t.replace("#",""),s=Math.min(255,Math.floor(parseInt(e.substr(0,2),16)*(1+i))),h=Math.min(255,Math.floor(parseInt(e.substr(2,2),16)*(1+i))),n=Math.min(255,Math.floor(parseInt(e.substr(4,2),16)*(1+i)));return`rgb(${s}, ${h}, ${n})`}}class x{constructor(t,i){this.owner=t,this.gameObjectManager=i,this.fireRate=300,this.lastFireTime=0,this.canFire=!0,this.projectileSpeed=600,this.projectileDamage=1,this.projectileLifetime=3e3,this.projectileType="player",this.currentPattern="single",this.spreadAngle=Math.PI/6,this.muzzleFlashDuration=100,this.muzzleFlashTime=0,this.muzzleFlashPositions=[],this.fireSound=null,this.soundVolume=.5,this.initializeProjectilePool(),console.log("WeaponSystem initialized for owner:",t.constructor.name)}initializeProjectilePool(){this.gameObjectManager.pools.has("projectile")||this.gameObjectManager.createPool("projectile",()=>new w,t=>t.reset(),20)}update(t){this.canFire||(this.lastFireTime+=t,this.lastFireTime>=this.fireRate&&(this.canFire=!0,this.lastFireTime=0)),this.muzzleFlashTime>0&&(this.muzzleFlashTime-=t,this.muzzleFlashTime<=0&&(this.muzzleFlashPositions=[]))}fire(t=o.up()){if(console.log("WeaponSystem.fire() called, canFire:",this.canFire),!this.canFire)return console.log("Cannot fire - weapon on cooldown"),!1;switch(this.currentPattern){case"single":this.fireSingle(t);break;case"double":this.fireDouble(t);break;case"triple":this.fireTriple(t);break;case"spread":this.fireSpread(t);break;default:this.fireSingle(t);break}return this.canFire=!1,this.lastFireTime=0,this.triggerMuzzleFlash(),this.playFireSound(),!0}fireSingle(t){const i=this.getFirePosition();this.createProjectile(i,t)}fireDouble(t){const i=this.getFirePosition(),e=t.perpendicular().multiply(8);this.createProjectile(i.subtract(e),t),this.createProjectile(i.add(e),t)}fireTriple(t){const i=this.getFirePosition(),e=t.perpendicular().multiply(12);this.createProjectile(i,t),this.createProjectile(i.subtract(e),t),this.createProjectile(i.add(e),t)}fireSpread(t){const i=this.getFirePosition(),e=t.angle(),s=this.spreadAngle/2;for(let h=-2;h<=2;h++){const n=e+h*s,a=o.fromAngle(n);this.createProjectile(i,a)}}createProjectile(t,i){const e=new w;e.initialize(t,i,this.projectileSpeed,this.projectileType,this.owner),e.damage=this.projectileDamage,e.lifetime=this.projectileLifetime,this.gameObjectManager.add(e),console.log("Projectile created at:",e.position.toString(),"with velocity:",e.velocity.toString())}getFirePosition(){const t=this.owner.position.clone(),i=new o(0,-this.owner.height/2-5);return t.add(i)}triggerMuzzleFlash(){switch(this.muzzleFlashTime=this.muzzleFlashDuration,this.muzzleFlashPositions=[],this.currentPattern){case"single":this.muzzleFlashPositions.push(this.getFirePosition());break;case"double":const t=this.getFirePosition(),i=new o(8,0);this.muzzleFlashPositions.push(t.subtract(i)),this.muzzleFlashPositions.push(t.add(i));break;case"triple":const e=this.getFirePosition(),s=new o(12,0);this.muzzleFlashPositions.push(e),this.muzzleFlashPositions.push(e.subtract(s)),this.muzzleFlashPositions.push(e.add(s));break;case"spread":this.muzzleFlashPositions.push(this.getFirePosition());break}}playFireSound(){this.fireSound&&typeof this.fireSound.play=="function"&&(this.fireSound.volume=this.soundVolume,this.fireSound.currentTime=0,this.fireSound.play().catch(t=>{console.warn("Could not play fire sound:",t)}))}render(t){this.muzzleFlashTime>0&&this.renderMuzzleFlash(t)}renderMuzzleFlash(t){const i=this.muzzleFlashTime/this.muzzleFlashDuration;t.save(),t.globalAlpha=i;for(const e of this.muzzleFlashPositions){const s=t.createRadialGradient(e.x,e.y,0,e.x,e.y,15);s.addColorStop(0,"#FFFFFF"),s.addColorStop(.3,"#FFD700"),s.addColorStop(.6,"#FF6B35"),s.addColorStop(1,"rgba(255, 107, 53, 0)"),t.fillStyle=s,t.beginPath(),t.arc(e.x,e.y,15,0,Math.PI*2),t.fill(),t.fillStyle="#FFFFFF",t.beginPath(),t.arc(e.x,e.y,3,0,Math.PI*2),t.fill()}t.restore()}setPattern(t){["single","double","triple","spread"].includes(t)?(this.currentPattern=t,console.log(`Weapon pattern changed to: ${t}`)):console.warn(`Invalid weapon pattern: ${t}`)}setFireRate(t){this.fireRate=Math.max(50,t)}setProjectileSpeed(t){this.projectileSpeed=Math.max(100,t)}setProjectileDamage(t){this.projectileDamage=Math.max(1,t)}setSpreadAngle(t){this.spreadAngle=Math.max(0,Math.min(Math.PI,t))}isReady(){return this.canFire}getCooldownProgress(){return this.canFire?1:this.lastFireTime/this.fireRate}resetCooldown(){this.canFire=!0,this.lastFireTime=0}getStats(){return{pattern:this.currentPattern,fireRate:this.fireRate,projectileSpeed:this.projectileSpeed,projectileDamage:this.projectileDamage,isReady:this.canFire,cooldownProgress:this.getCooldownProgress()}}}class j extends v{constructor(t,i,e,s,h=null){super(t,i),this.canvasWidth=e,this.canvasHeight=s,this.maxSpeed=300,this.acceleration=800,this.friction=.85,this.maxHealth=100,this.health=this.maxHealth,this.maxLives=3,this.lives=this.maxLives,this.isInvulnerable=!1,this.invulnerabilityDuration=2e3,this.invulnerabilityTimer=0,this.isDestroyed=!1,this.damageFlashTimer=0,this.damageFlashDuration=200,this.isFlashing=!1,this.width=32,this.height=48,this.collisionRadius=16,this.boundaryPadding=Math.max(this.width,this.height)/2,this.animationTime=0,this.thrusterAnimationSpeed=8,this.isMoving=!1,this.movementInput=new o(0,0),this.weaponSystem=null,h&&(this.weaponSystem=new x(this,h)),this.addTag("player"),console.log("PlayerShip created at position:",this.position.toString())}update(t,i=new o(0,0)){if(this.active){if(this.movementInput=i.clone(),this.isMoving=i.magnitude()>.1,this.isMoving){const h=i.multiply(this.maxSpeed).subtract(this.velocity).multiply(this.acceleration*t/1e3);this.velocity.addInPlace(h),this.velocity.magnitude()>this.maxSpeed&&(this.velocity=this.velocity.normalize().multiply(this.maxSpeed))}else this.velocity.multiplyInPlace(Math.pow(this.friction,t/16.67)),this.velocity.magnitude()<1&&this.velocity.set(0,0);this.position.addInPlace(this.velocity.multiply(t/1e3)),this.checkBoundaries(),this.animationTime+=t/1e3,this.updateHealthSystem(t),this.updateCollisionBounds(),this.weaponSystem&&this.weaponSystem.update(t)}}checkBoundaries(){const t=this.boundaryPadding,i=this.canvasWidth-this.boundaryPadding,e=this.boundaryPadding,s=this.canvasHeight-this.boundaryPadding;this.position.x<t?(this.position.x=t,this.velocity.x=Math.max(0,this.velocity.x)):this.position.x>i&&(this.position.x=i,this.velocity.x=Math.min(0,this.velocity.x)),this.position.y<e?(this.position.y=e,this.velocity.y=Math.max(0,this.velocity.y)):this.position.y>s&&(this.position.y=s,this.velocity.y=Math.min(0,this.velocity.y))}render(t,i=0){if(!this.visible)return;const e=this.position.add(this.velocity.multiply(i/1e3));t.save(),t.translate(e.x,e.y),t.rotate(this.rotation),this.drawShipBody(t),this.isMoving&&this.drawThrusterEffects(t),window.DEBUG_MODE&&this.drawDebugInfo(t),t.restore(),this.weaponSystem&&this.weaponSystem.render(t)}drawShipBody(t){let i=1,e="#4A90E2",s="#2E5C8A";if(this.isInvulnerable){const n=Math.sin(this.invulnerabilityTimer*8*Math.PI/1e3);i=.3+.7*Math.abs(n)}if(this.isFlashing){const h=this.damageFlashTimer/this.damageFlashDuration;e=this.interpolateColor("#4A90E2","#FF4444",h),s=this.interpolateColor("#2E5C8A","#CC2222",h)}t.globalAlpha=i,t.fillStyle=e,t.strokeStyle=s,t.lineWidth=2,t.beginPath(),t.moveTo(0,-this.height/2),t.lineTo(-this.width/3,this.height/3),t.lineTo(this.width/3,this.height/3),t.closePath(),t.fill(),t.stroke(),t.fillStyle="#7BB3F0",t.beginPath(),t.ellipse(0,-this.height/4,this.width/6,this.height/8,0,0,Math.PI*2),t.fill(),t.fillStyle="#2E5C8A",t.fillRect(-this.width/4,this.height/6,this.width/8,this.height/4),t.fillRect(this.width/8,this.height/6,this.width/8,this.height/4),t.fillStyle="#87CEEB",t.beginPath(),t.ellipse(-this.width/6,this.height/3,3,6,0,0,Math.PI*2),t.ellipse(this.width/6,this.height/3,3,6,0,0,Math.PI*2),t.fill(),t.globalAlpha=1}drawThrusterEffects(t){const i=this.velocity.magnitude()/this.maxSpeed,e=Math.sin(this.animationTime*this.thrusterAnimationSpeed*Math.PI*2),s=15*i,h=5*e*i,n=s+h;n>2&&(this.drawThrusterFlame(t,-this.width/6,this.height/3,n),this.drawThrusterFlame(t,this.width/6,this.height/3,n)),this.drawDirectionalThrusters(t,i,e)}drawThrusterFlame(t,i,e,s){const h=t.createLinearGradient(i,e,i,e+s);h.addColorStop(0,"#FFD700"),h.addColorStop(.5,"#FF6B35"),h.addColorStop(1,"rgba(255, 0, 0, 0)"),t.fillStyle=h,t.beginPath(),t.moveTo(i-3,e),t.lineTo(i+3,e),t.lineTo(i+1,e+s),t.lineTo(i-1,e+s),t.closePath(),t.fill()}drawDirectionalThrusters(t,i,e){const s=3*i,h=.7*i;if(Math.abs(this.movementInput.x)>.1&&(t.fillStyle=`rgba(135, 206, 235, ${h})`,this.movementInput.x>0?t.fillRect(-this.width/2-s,-2,s,4):t.fillRect(this.width/2,-2,s,4)),this.movementInput.y<-.1){t.fillStyle=`rgba(255, 215, 0, ${h})`;const n=8*i*(1+.3*e);t.fillRect(-2,-this.height/2-n,4,n)}}drawDebugInfo(t){if(t.strokeStyle="#FF0000",t.lineWidth=1,t.beginPath(),t.arc(0,0,this.collisionRadius,0,Math.PI*2),t.stroke(),this.velocity.magnitude()>1){t.strokeStyle="#00FF00",t.lineWidth=2,t.beginPath(),t.moveTo(0,0);const i=.1;t.lineTo(this.velocity.x*i,this.velocity.y*i),t.stroke()}t.fillStyle="#FFFF00",t.fillRect(-1,-1,2,2)}getCurrentSpeed(){return this.velocity.magnitude()}getBoundaryStatus(){const t=this.boundaryPadding,i=this.canvasWidth-this.boundaryPadding,e=this.boundaryPadding,s=this.canvasHeight-this.boundaryPadding;return{left:this.position.x<=t,right:this.position.x>=i,top:this.position.y<=e,bottom:this.position.y>=s}}resetToPosition(t,i){this.position.set(t,i),this.velocity.set(0,0),this.rotation=0,this.animationTime=0,this.isMoving=!1,this.movementInput.set(0,0),this.active=!0,this.visible=!0,this.destroyed=!1}updateCanvasDimensions(t,i){this.canvasWidth=t,this.canvasHeight=i,this.checkBoundaries()}fire(t=o.up()){if(console.log("PlayerShip.fire() called, weaponSystem exists:",!!this.weaponSystem),this.weaponSystem){const i=this.weaponSystem.fire(t);return console.log("WeaponSystem.fire() returned:",i),i}return!1}setWeaponSystem(t){this.weaponSystem=t}getWeaponSystem(){return this.weaponSystem}canFire(){return this.weaponSystem?this.weaponSystem.isReady():!1}setWeaponPattern(t){this.weaponSystem&&this.weaponSystem.setPattern(t)}getWeaponStats(){return this.weaponSystem?this.weaponSystem.getStats():null}updateHealthSystem(t){this.isInvulnerable&&(this.invulnerabilityTimer-=t,this.invulnerabilityTimer<=0&&(this.isInvulnerable=!1,this.invulnerabilityTimer=0)),this.isFlashing&&(this.damageFlashTimer-=t,this.damageFlashTimer<=0&&(this.isFlashing=!1,this.damageFlashTimer=0))}takeDamage(t){if(this.isInvulnerable||this.isDestroyed)return{damageTaken:0,health:this.health,lives:this.lives,destroyed:this.isDestroyed};const i=Math.min(t,this.health);return this.health-=i,this.isFlashing=!0,this.damageFlashTimer=this.damageFlashDuration,console.log(`PlayerShip took ${i} damage. Health: ${this.health}/${this.maxHealth}, Lives: ${this.lives}`),this.health<=0?this.destroyShip():(this.isInvulnerable=!0,this.invulnerabilityTimer=this.invulnerabilityDuration),{damageTaken:i,health:this.health,lives:this.lives,destroyed:this.isDestroyed}}destroyShip(){this.health=0,this.lives--,console.log(`PlayerShip destroyed! Lives remaining: ${this.lives}`),this.lives<=0?(this.isDestroyed=!0,this.active=!1,console.log("Game Over - No lives remaining")):this.respawn()}respawn(){this.health=this.maxHealth;const t=this.canvasWidth/2,i=this.canvasHeight-100;this.resetToPosition(t,i),this.isInvulnerable=!0,this.invulnerabilityTimer=this.invulnerabilityDuration*2,this.isFlashing=!1,this.damageFlashTimer=0,console.log(`PlayerShip respawned with full health. Lives: ${this.lives}`)}heal(t){if(this.isDestroyed)return 0;const i=Math.min(t,this.maxHealth-this.health);return this.health+=i,console.log(`PlayerShip healed for ${i}. Health: ${this.health}/${this.maxHealth}`),i}addLives(t){this.lives+=t,console.log(`PlayerShip gained ${t} lives. Total lives: ${this.lives}`)}getHealthStatus(){return{health:this.health,maxHealth:this.maxHealth,healthPercentage:this.health/this.maxHealth,lives:this.lives,maxLives:this.maxLives,isInvulnerable:this.isInvulnerable,invulnerabilityTimeRemaining:this.invulnerabilityTimer,isDestroyed:this.isDestroyed,isFlashing:this.isFlashing}}resetHealthAndLives(){this.health=this.maxHealth,this.lives=this.maxLives,this.isInvulnerable=!1,this.invulnerabilityTimer=0,this.isDestroyed=!1,this.isFlashing=!1,this.damageFlashTimer=0,console.log("PlayerShip health and lives reset to maximum")}interpolateColor(t,i,e){e=Math.max(0,Math.min(1,e));const s=t.replace("#",""),h=i.replace("#",""),n=parseInt(s.substr(0,2),16),a=parseInt(s.substr(2,2),16),l=parseInt(s.substr(4,2),16),c=parseInt(h.substr(0,2),16),p=parseInt(h.substr(2,2),16),u=parseInt(h.substr(4,2),16),d=Math.round(n+(c-n)*e),g=Math.round(a+(p-a)*e),f=Math.round(l+(u-l)*e),m=M=>{const b=M.toString(16);return b.length===1?"0"+b:b};return`#${m(d)}${m(g)}${m(f)}`}}class D{constructor(t,i,e=10){this.createFn=t,this.resetFn=i,this.pool=[],this.active=[];for(let s=0;s<e;s++)this.pool.push(this.createFn())}get(){let t;return this.pool.length>0?t=this.pool.pop():t=this.createFn(),this.active.push(t),t}release(t){const i=this.active.indexOf(t);i!==-1&&(this.active.splice(i,1),this.resetFn(t),this.pool.push(t))}releaseAll(){for(;this.active.length>0;){const t=this.active.pop();this.resetFn(t),this.pool.push(t)}}getStats(){return{pooled:this.pool.length,active:this.active.length,total:this.pool.length+this.active.length}}}class I{constructor(){this.objects=[],this.objectsToAdd=[],this.objectsToRemove=[],this.pools=new Map}add(t){this.objectsToAdd.push(t)}remove(t){this.objectsToRemove.push(t)}createPool(t,i,e,s=10){this.pools.set(t,new D(i,e,s))}getFromPool(t){const i=this.pools.get(t);if(i)return i.get();throw new Error(`Pool for type '${t}' not found`)}returnToPool(t,i){const e=this.pools.get(t);e&&(e.release(i),this.remove(i))}update(t){this.processAdditions(),this.processRemovals();for(let i=this.objects.length-1;i>=0;i--){const e=this.objects[i];if(e.destroyed){this.objectsToRemove.push(e);continue}e.active&&e.update(t)}}render(t,i=0){for(const e of this.objects)e.visible&&!e.destroyed&&e.render(t,i)}processAdditions(){this.objectsToAdd.length>0&&(this.objects.push(...this.objectsToAdd),this.objectsToAdd.length=0)}processRemovals(){if(this.objectsToRemove.length>0){for(const t of this.objectsToRemove){const i=this.objects.indexOf(t);i!==-1&&this.objects.splice(i,1)}this.objectsToRemove.length=0}}findByTag(t){return this.objects.filter(i=>i.hasTag(t))}findById(t){return this.objects.find(i=>i.id===t)}getActive(){return this.objects.filter(t=>t.active&&!t.destroyed)}getVisible(){return this.objects.filter(t=>t.visible&&!t.destroyed)}checkCollisions(t,i,e){const s=this.findByTag(t),h=this.findByTag(i);for(const n of s)if(n.active)for(const a of h)a.active&&n.collidesWith(a)&&e(n,a)}checkCollisionsOptimized(t,i,e,s=64){const h=this.findByTag(t),n=this.findByTag(i),a=new Map;for(const l of n){if(!l.active)continue;const c=Math.floor(l.position.x/s),p=Math.floor(l.position.y/s),u=`${c},${p}`;a.has(u)||a.set(u,[]),a.get(u).push(l)}for(const l of h){if(!l.active)continue;const c=Math.floor(l.position.x/s),p=Math.floor(l.position.y/s);for(let u=-1;u<=1;u++)for(let d=-1;d<=1;d++){const g=`${c+u},${p+d}`,f=a.get(g);if(f)for(const m of f)l.collidesWith(m)&&e(l,m)}}}clear(){this.objects.length=0,this.objectsToAdd.length=0,this.objectsToRemove.length=0;for(const t of this.pools.values())t.releaseAll()}getStats(){const t={};for(const[i,e]of this.pools.entries())t[i]=e.getStats();return{totalObjects:this.objects.length,activeObjects:this.getActive().length,visibleObjects:this.getVisible().length,pendingAdditions:this.objectsToAdd.length,pendingRemovals:this.objectsToRemove.length,pools:t}}}class R{constructor(t,i){this.canvas=t,this.ctx=t.getContext("2d"),this.uiElement=i,this.isRunning=!1,this.isPaused=!1,this.targetFPS=60,this.fixedTimeStep=1e3/this.targetFPS,this.maxFrameTime=250,this.lastFrameTime=0,this.accumulator=0,this.currentTime=0,this.frameCount=0,this.fpsTimer=0,this.currentFPS=0,this.gameLoop=this.gameLoop.bind(this)}async init(){return console.log("Initializing WarpSpace Game Engine..."),this.setupCanvas(),this.initializeSystems(),this.start(),Promise.resolve()}setupCanvas(){this.ctx.imageSmoothingEnabled=!1,this.canvas.width=800,this.canvas.height=600,console.log(`Canvas initialized: ${this.canvas.width}x${this.canvas.height}`)}initializeSystems(){this.inputManager=new S(this.canvas),this.gameObjectManager=new I;const t=this.canvas.width/2,i=this.canvas.height-100;this.playerShip=new j(t,i,this.canvas.width,this.canvas.height,this.gameObjectManager),console.log("Game systems initialized")}start(){this.isRunning||(this.isRunning=!0,this.isPaused=!1,this.currentTime=performance.now(),this.lastFrameTime=this.currentTime,this.accumulator=0,this.frameCount=0,this.fpsTimer=0,requestAnimationFrame(this.gameLoop),console.log("Game loop started with fixed timestep"))}pause(){this.isPaused=!0,console.log("Game paused")}resume(){this.isPaused&&(this.isPaused=!1,this.currentTime=performance.now(),this.lastFrameTime=this.currentTime,this.accumulator=0,console.log("Game resumed"))}destroy(){this.isRunning=!1,this.inputManager&&this.inputManager.destroy(),console.log("Game engine destroyed")}gameLoop(t){if(!this.isRunning)return;let i=t-this.lastFrameTime;if(i>this.maxFrameTime&&(i=this.maxFrameTime),this.lastFrameTime=t,!this.isPaused){for(this.accumulator+=i;this.accumulator>=this.fixedTimeStep;)this.update(this.fixedTimeStep),this.accumulator-=this.fixedTimeStep;const e=this.accumulator/this.fixedTimeStep;this.render(e),this.updateFPSCounter(i)}requestAnimationFrame(this.gameLoop)}update(t){if(this.inputManager&&this.inputManager.update(),this.playerShip&&this.inputManager){const i=this.inputManager.getMovementVector();this.playerShip.update(t,i),this.inputManager.isActionDown("fire")&&this.playerShip.fire(),this.inputManager.isKeyPressed("KeyD")&&this.playerShip.takeDamage(25),this.inputManager.isKeyPressed("KeyH")&&this.playerShip.heal(25),this.inputManager.isKeyPressed("KeyL")&&this.playerShip.addLives(1)}this.gameObjectManager&&this.gameObjectManager.update(t),this.cleanupProjectiles()}cleanupProjectiles(){if(!this.gameObjectManager)return;const t={left:-50,right:this.canvas.width+50,top:-50,bottom:this.canvas.height+50},i=this.gameObjectManager.findByTag("projectile");for(const e of i)e.isOutOfBounds(t)&&this.gameObjectManager.returnToPool("projectile",e)}render(t=0){this.ctx.fillStyle="#000011",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.renderStarField(),this.playerShip&&this.playerShip.render(this.ctx,t),this.gameObjectManager&&this.gameObjectManager.render(this.ctx,t),this.ctx.fillStyle="#ffffff",this.ctx.font="20px Arial",this.ctx.textAlign="center",this.ctx.fillText("WarpSpace",this.canvas.width/2,30),this.inputManager&&(this.renderInputDebug(),this.inputManager.render(this.ctx)),this.ctx.font="12px Arial",this.ctx.textAlign="left",this.ctx.fillText(`FPS: ${this.currentFPS}`,10,20),this.renderHealthAndLivesUI()}renderInputDebug(){if(!this.inputManager)return;const t=this.inputManager.getMovementVector();this.ctx.font="12px Arial",this.ctx.textAlign="left",this.ctx.fillStyle="#ffffff",this.ctx.fillText(`Movement: ${t.x.toFixed(2)}, ${t.y.toFixed(2)}`,10,40);const i=["fire","pause","interact"];let e=60;for(const s of i)this.inputManager.isActionDown(s)?(this.ctx.fillStyle="#00ff00",this.ctx.fillText(`${s.toUpperCase()}: ACTIVE`,10,e)):(this.ctx.fillStyle="#666666",this.ctx.fillText(`${s}: inactive`,10,e)),e+=15;if(this.ctx.fillStyle="#ffffff",this.ctx.fillText(`Mouse: ${this.inputManager.mousePosition.x.toFixed(0)}, ${this.inputManager.mousePosition.y.toFixed(0)}`,10,e+10),this.inputManager.isTouchDevice&&this.ctx.fillText(`Touch Device: ${this.inputManager.touches.size} touches`,10,e+25),this.gameObjectManager){const s=this.gameObjectManager.getStats();this.ctx.fillText(`Objects: ${s.totalObjects} (${s.activeObjects} active)`,10,e+40);const h=this.gameObjectManager.findByTag("projectile");this.ctx.fillText(`Projectiles: ${h.length}`,10,e+55)}}updateFPSCounter(t){this.frameCount++,this.fpsTimer+=t,this.fpsTimer>=1e3&&(this.currentFPS=Math.round(this.frameCount*1e3/this.fpsTimer),this.frameCount=0,this.fpsTimer=0)}renderStarField(){this.ctx.fillStyle="#ffffff";for(let t=0;t<100;t++){const i=t*37%this.canvas.width,e=t*73%this.canvas.height,s=t%3+1;this.ctx.fillRect(i,e,s,s)}}renderHealthAndLivesUI(){if(!this.playerShip)return;const t=this.playerShip.getHealthStatus(),i=this.canvas.width-220,e=15,s=200,h=20;this.ctx.fillStyle="#333333",this.ctx.fillRect(i,e,s,h),this.ctx.strokeStyle="#666666",this.ctx.lineWidth=2,this.ctx.strokeRect(i,e,s,h);const n=s*t.healthPercentage;let a="#00ff00";t.healthPercentage<.3?a="#ff0000":t.healthPercentage<.6&&(a="#ffaa00"),this.ctx.fillStyle=a,this.ctx.fillRect(i+2,e+2,n-4,h-4),this.ctx.fillStyle="#ffffff",this.ctx.font="12px Arial",this.ctx.textAlign="center",this.ctx.fillText(`${t.health}/${t.maxHealth}`,i+s/2,e+h/2+4);const l=e+h+25;this.ctx.textAlign="right",this.ctx.fillStyle="#ffffff",this.ctx.font="16px Arial",this.ctx.fillText(`Lives: ${t.lives}`,this.canvas.width-20,l);const c=16,p=20,u=this.canvas.width-20-t.lives*p;for(let d=0;d<t.lives;d++){const g=u+d*p,f=l+10;this.ctx.fillStyle="#4A90E2",this.ctx.beginPath(),this.ctx.moveTo(g,f-c/2),this.ctx.lineTo(g-c/3,f+c/3),this.ctx.lineTo(g+c/3,f+c/3),this.ctx.closePath(),this.ctx.fill()}if(t.isInvulnerable){this.ctx.fillStyle="#ffff00",this.ctx.font="12px Arial",this.ctx.textAlign="right";const d=(t.invulnerabilityTimeRemaining/1e3).toFixed(1);this.ctx.fillText(`Invulnerable: ${d}s`,this.canvas.width-20,l+50)}t.isDestroyed&&(this.ctx.fillStyle="rgba(0, 0, 0, 0.7)",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.ctx.fillStyle="#ff0000",this.ctx.font="48px Arial",this.ctx.textAlign="center",this.ctx.fillText("GAME OVER",this.canvas.width/2,this.canvas.height/2),this.ctx.fillStyle="#ffffff",this.ctx.font="24px Arial",this.ctx.fillText("No lives remaining",this.canvas.width/2,this.canvas.height/2+50))}handleResize(){console.log("Window resized")}}document.addEventListener("DOMContentLoaded",()=>{const r=document.getElementById("gameCanvas"),t=document.getElementById("ui");if(!r){console.error("Game canvas not found");return}const i=new R(r,t);i.init().then(()=>{console.log("WarpSpace game initialized successfully"),t.innerHTML="<div>WarpSpace - Ready to Play</div>"}).catch(e=>{console.error("Failed to initialize game:",e),t.innerHTML="<div>Error: Failed to load game</div>"}),window.addEventListener("resize",()=>{i.handleResize()}),document.addEventListener("visibilitychange",()=>{document.hidden?i.pause():i.resume()})});
//# sourceMappingURL=index-CXHmaMUw.js.map
